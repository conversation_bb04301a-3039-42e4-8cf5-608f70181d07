"""
Comprehensive configuration management for Agno-Euri Project.
Handles environment variables, validation, and configuration loading.
"""

import os
from typing import List, Optional, Union
from pathlib import Path

from pydantic import BaseSettings, Field, validator
from pydantic_settings import BaseSettings as PydanticBaseSettings


class EuriConfig(BaseSettings):
    """Euri API configuration."""
    
    api_key: str = Field(..., env="EURI_API_KEY", description="Euri API key")
    client_url: str = Field(
        "https://api.euri.com/v1", 
        env="EURI_CLIENT_URL", 
        description="Euri client base URL"
    )
    timeout: int = Field(30, env="EURI_TIMEOUT", description="Request timeout in seconds")
    max_retries: int = Field(3, env="EURI_MAX_RETRIES", description="Maximum retry attempts")
    retry_delay: float = Field(1.0, env="EURI_RETRY_DELAY", description="Delay between retries")
    
    class Config:
        env_prefix = "EURI_"


class AIModelConfig(BaseSettings):
    """AI model configuration."""
    
    # OpenAI Configuration
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    openai_model: str = Field("gpt-4-turbo-preview", env="OPENAI_MODEL")
    openai_max_tokens: int = Field(4096, env="OPENAI_MAX_TOKENS")
    openai_temperature: float = Field(0.7, env="OPENAI_TEMPERATURE")
    
    # Anthropic Configuration
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    anthropic_model: str = Field("claude-3-sonnet-20240229", env="ANTHROPIC_MODEL")
    
    # Agno Configuration
    agno_model: str = Field("openai", env="AGNO_MODEL")
    agno_telemetry: bool = Field(False, env="AGNO_TELEMETRY")
    agno_debug: bool = Field(False, env="AGNO_DEBUG")
    agno_log_level: str = Field("INFO", env="AGNO_LOG_LEVEL")
    
    @validator("agno_model")
    def validate_agno_model(cls, v):
        allowed_models = ["openai", "anthropic", "claude", "gpt"]
        if v not in allowed_models:
            raise ValueError(f"agno_model must be one of {allowed_models}")
        return v


class CacheConfig(BaseSettings):
    """Caching configuration."""
    
    # Redis Configuration
    redis_url: str = Field("redis://localhost:6379/0", env="REDIS_URL")
    redis_password: Optional[str] = Field(None, env="REDIS_PASSWORD")
    redis_max_connections: int = Field(20, env="REDIS_MAX_CONNECTIONS")
    redis_socket_timeout: int = Field(5, env="REDIS_SOCKET_TIMEOUT")
    redis_socket_connect_timeout: int = Field(5, env="REDIS_SOCKET_CONNECT_TIMEOUT")
    
    # Cache TTL Settings (in seconds)
    cache_ttl_default: int = Field(3600, env="CACHE_TTL_DEFAULT")  # 1 hour
    cache_ttl_conversations: int = Field(86400, env="CACHE_TTL_CONVERSATIONS")  # 24 hours
    cache_ttl_embeddings: int = Field(604800, env="CACHE_TTL_EMBEDDINGS")  # 7 days
    cache_ttl_api_responses: int = Field(1800, env="CACHE_TTL_API_RESPONSES")  # 30 minutes
    cache_max_size: int = Field(1000, env="CACHE_MAX_SIZE")
    
    class Config:
        env_prefix = "CACHE_"


class DatabaseConfig(BaseSettings):
    """Database configuration."""
    
    database_url: str = Field(
        "sqlite+aiosqlite:///./data/agno_euri.db", 
        env="DATABASE_URL",
        description="Database connection URL"
    )
    
    class Config:
        env_prefix = "DATABASE_"


class APIConfig(BaseSettings):
    """API server configuration."""
    
    host: str = Field("0.0.0.0", env="API_HOST")
    port: int = Field(8000, env="API_PORT")
    reload: bool = Field(True, env="API_RELOAD")
    workers: int = Field(1, env="API_WORKERS")
    log_level: str = Field("info", env="API_LOG_LEVEL")
    
    # Security
    secret_key: str = Field(..., env="SECRET_KEY", description="Secret key for JWT tokens")
    algorithm: str = Field("HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # CORS
    cors_origins: List[str] = Field(
        ["http://localhost:3000", "http://localhost:8080"], 
        env="CORS_ORIGINS"
    )
    cors_allow_credentials: bool = Field(True, env="CORS_ALLOW_CREDENTIALS")
    cors_allow_methods: List[str] = Field(["*"], env="CORS_ALLOW_METHODS")
    cors_allow_headers: List[str] = Field(["*"], env="CORS_ALLOW_HEADERS")
    
    class Config:
        env_prefix = "API_"


class MemoryConfig(BaseSettings):
    """Memory and session configuration."""
    
    session_timeout: int = Field(3600, env="SESSION_TIMEOUT")  # 1 hour
    max_conversation_history: int = Field(50, env="MAX_CONVERSATION_HISTORY")
    max_session_memory_size: int = Field(10485760, env="MAX_SESSION_MEMORY_SIZE")  # 10MB
    
    memory_backend: str = Field("redis", env="MEMORY_BACKEND")
    memory_compression: bool = Field(True, env="MEMORY_COMPRESSION")
    memory_encryption: bool = Field(False, env="MEMORY_ENCRYPTION")
    
    @validator("memory_backend")
    def validate_memory_backend(cls, v):
        allowed_backends = ["redis", "sqlite", "memory"]
        if v not in allowed_backends:
            raise ValueError(f"memory_backend must be one of {allowed_backends}")
        return v
    
    class Config:
        env_prefix = "MEMORY_"


class LoggingConfig(BaseSettings):
    """Logging configuration."""
    
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_format: str = Field("json", env="LOG_FORMAT")
    log_file: str = Field("logs/agno_euri.log", env="LOG_FILE")
    log_max_size: int = Field(10485760, env="LOG_MAX_SIZE")  # 10MB
    log_backup_count: int = Field(5, env="LOG_BACKUP_COUNT")
    
    class Config:
        env_prefix = "LOG_"


class MonitoringConfig(BaseSettings):
    """Monitoring and metrics configuration."""
    
    enable_metrics: bool = Field(True, env="ENABLE_METRICS")
    metrics_port: int = Field(9090, env="METRICS_PORT")
    health_check_interval: int = Field(30, env="HEALTH_CHECK_INTERVAL")
    
    class Config:
        env_prefix = "MONITORING_"


class SecurityConfig(BaseSettings):
    """Security configuration."""
    
    # Rate Limiting
    rate_limit_requests: int = Field(100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(60, env="RATE_LIMIT_WINDOW")  # seconds
    
    # Request Validation
    max_request_size: int = Field(10485760, env="MAX_REQUEST_SIZE")  # 10MB
    allowed_file_types: List[str] = Field(
        ["txt", "pdf", "docx", "md"], 
        env="ALLOWED_FILE_TYPES"
    )
    
    class Config:
        env_prefix = "SECURITY_"


class FeatureFlags(BaseSettings):
    """Feature flags configuration."""
    
    enable_caching: bool = Field(True, env="ENABLE_CACHING")
    enable_memory_persistence: bool = Field(True, env="ENABLE_MEMORY_PERSISTENCE")
    enable_conversation_history: bool = Field(True, env="ENABLE_CONVERSATION_HISTORY")
    enable_embeddings_cache: bool = Field(True, env="ENABLE_EMBEDDINGS_CACHE")
    enable_api_response_cache: bool = Field(True, env="ENABLE_API_RESPONSE_CACHE")
    enable_metrics_collection: bool = Field(True, env="ENABLE_METRICS_COLLECTION")
    enable_detailed_logging: bool = Field(True, env="ENABLE_DETAILED_LOGGING")
    
    class Config:
        env_prefix = "FEATURE_"


class Settings(PydanticBaseSettings):
    """Main settings class that combines all configuration sections."""
    
    # Environment
    environment: str = Field("development", env="ENVIRONMENT")
    debug: bool = Field(True, env="DEBUG")
    testing: bool = Field(False, env="TESTING")
    
    # Configuration sections
    euri: EuriConfig = EuriConfig()
    ai_model: AIModelConfig = AIModelConfig()
    cache: CacheConfig = CacheConfig()
    database: DatabaseConfig = DatabaseConfig()
    api: APIConfig = APIConfig()
    memory: MemoryConfig = MemoryConfig()
    logging: LoggingConfig = LoggingConfig()
    monitoring: MonitoringConfig = MonitoringConfig()
    security: SecurityConfig = SecurityConfig()
    features: FeatureFlags = FeatureFlags()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure required directories exist."""
        directories = [
            Path("logs"),
            Path("data"),
            Path(self.logging.log_file).parent,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @property
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.environment.lower() == "development"
    
    @property
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self.environment.lower() == "production"
    
    @property
    def is_testing(self) -> bool:
        """Check if running in testing mode."""
        return self.testing or self.environment.lower() == "testing"


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment variables."""
    global settings
    settings = Settings()
    return settings
