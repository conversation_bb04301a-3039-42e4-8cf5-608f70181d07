"""
Advanced conversation memory management with persistence and context awareness.
"""

import json
import pickle
import gzip
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from uuid import uuid4

import redis.asyncio as redis
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import Column, String, DateTime, Text, Integer, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy import select, delete, update

from ..config.settings import get_settings
from ..utils.logger import get_logger


Base = declarative_base()


@dataclass
class ConversationMessage:
    """Represents a single conversation message."""
    id: str
    session_id: str
    user_id: str
    role: str  # 'user' or 'assistant'
    content: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversationMessage':
        """Create from dictionary."""
        return cls(**data)


class ConversationRecord(Base):
    """SQLAlchemy model for conversation storage."""
    __tablename__ = "conversations"
    
    id = Column(String, primary_key=True)
    session_id = Column(String, index=True, nullable=False)
    user_id = Column(String, index=True, nullable=False)
    role = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    timestamp = Column(DateTime, nullable=False)
    metadata = Column(Text)  # JSON string
    compressed = Column(Boolean, default=False)


class ConversationMemory:
    """
    Advanced conversation memory system with multiple storage backends.
    Supports Redis for fast access and SQL database for persistence.
    """
    
    def __init__(
        self,
        user_id: str,
        session_id: Optional[str] = None,
        backend: Optional[str] = None
    ):
        """
        Initialize conversation memory.
        
        Args:
            user_id: User identifier
            session_id: Session identifier (auto-generated if None)
            backend: Storage backend ('redis', 'sqlite', 'memory')
        """
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        
        self.user_id = user_id
        self.session_id = session_id or str(uuid4())
        self.backend = backend or self.settings.memory.memory_backend
        
        # Initialize storage backends
        self.redis_client = None
        self.db_engine = None
        self.db_session = None
        self.memory_store = {}  # In-memory fallback
        
        self._initialize_backends()
    
    def _initialize_backends(self):
        """Initialize storage backends based on configuration."""
        if self.backend in ["redis", "hybrid"]:
            try:
                self.redis_client = redis.from_url(
                    self.settings.cache.redis_url,
                    password=self.settings.cache.redis_password,
                    max_connections=self.settings.cache.redis_max_connections,
                    socket_timeout=self.settings.cache.redis_socket_timeout,
                    socket_connect_timeout=self.settings.cache.redis_socket_connect_timeout,
                    decode_responses=True
                )
                self.logger.info("Redis backend initialized for conversation memory")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Redis backend: {e}")
                self.backend = "memory"
        
        if self.backend in ["sqlite", "postgresql", "hybrid"]:
            try:
                self.db_engine = create_async_engine(
                    self.settings.database.database_url,
                    echo=self.settings.ai_model.agno_debug
                )
                self.db_session = sessionmaker(
                    self.db_engine,
                    class_=AsyncSession,
                    expire_on_commit=False
                )
                self.logger.info("Database backend initialized for conversation memory")
            except Exception as e:
                self.logger.warning(f"Failed to initialize database backend: {e}")
                if self.backend not in ["redis", "hybrid"]:
                    self.backend = "memory"
    
    async def add_message(
        self,
        role: str,
        content: str,
        timestamp: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Add a message to conversation memory.
        
        Args:
            role: Message role ('user' or 'assistant')
            content: Message content
            timestamp: Message timestamp (current time if None)
            metadata: Additional metadata
        
        Returns:
            Message ID
        """
        message_id = str(uuid4())
        timestamp = timestamp or datetime.utcnow()
        
        message = ConversationMessage(
            id=message_id,
            session_id=self.session_id,
            user_id=self.user_id,
            role=role,
            content=content,
            timestamp=timestamp,
            metadata=metadata
        )
        
        try:
            # Store in Redis for fast access
            if self.redis_client:
                await self._store_message_redis(message)
            
            # Store in database for persistence
            if self.db_session:
                await self._store_message_db(message)
            
            # Fallback to memory
            if self.backend == "memory":
                self._store_message_memory(message)
            
            self.logger.debug(f"Added message {message_id} to conversation memory")
            return message_id
            
        except Exception as e:
            self.logger.error(f"Failed to add message to memory: {e}")
            raise
    
    async def _store_message_redis(self, message: ConversationMessage):
        """Store message in Redis."""
        key = f"conversation:{self.session_id}:{message.id}"
        data = json.dumps(message.to_dict(), default=str)
        
        if self.settings.memory.memory_compression:
            data = gzip.compress(data.encode()).hex()
        
        await self.redis_client.setex(
            key,
            self.settings.memory.session_timeout,
            data
        )
        
        # Add to session index
        session_key = f"session:{self.session_id}:messages"
        await self.redis_client.zadd(
            session_key,
            {message.id: message.timestamp.timestamp()}
        )
        await self.redis_client.expire(session_key, self.settings.memory.session_timeout)
    
    async def _store_message_db(self, message: ConversationMessage):
        """Store message in database."""
        async with self.db_session() as session:
            record = ConversationRecord(
                id=message.id,
                session_id=message.session_id,
                user_id=message.user_id,
                role=message.role,
                content=message.content,
                timestamp=message.timestamp,
                metadata=json.dumps(message.metadata) if message.metadata else None,
                compressed=False
            )
            
            # Compress large content
            if len(message.content) > 1000 and self.settings.memory.memory_compression:
                record.content = gzip.compress(message.content.encode()).hex()
                record.compressed = True
            
            session.add(record)
            await session.commit()
    
    def _store_message_memory(self, message: ConversationMessage):
        """Store message in memory."""
        if self.session_id not in self.memory_store:
            self.memory_store[self.session_id] = []
        
        self.memory_store[self.session_id].append(message.to_dict())
        
        # Limit memory usage
        max_messages = self.settings.memory.max_conversation_history
        if len(self.memory_store[self.session_id]) > max_messages:
            self.memory_store[self.session_id] = self.memory_store[self.session_id][-max_messages:]
    
    async def get_conversation_history(
        self,
        limit: Optional[int] = None,
        since: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        Get conversation history for the current session.
        
        Args:
            limit: Maximum number of messages to return
            since: Only return messages after this timestamp
        
        Returns:
            List of conversation messages
        """
        limit = limit or self.settings.memory.max_conversation_history
        
        try:
            # Try Redis first for speed
            if self.redis_client:
                messages = await self._get_messages_redis(limit, since)
                if messages:
                    return messages
            
            # Fallback to database
            if self.db_session:
                messages = await self._get_messages_db(limit, since)
                if messages:
                    return messages
            
            # Fallback to memory
            return self._get_messages_memory(limit, since)
            
        except Exception as e:
            self.logger.error(f"Failed to get conversation history: {e}")
            return []
    
    async def _get_messages_redis(
        self,
        limit: int,
        since: Optional[datetime]
    ) -> List[Dict[str, Any]]:
        """Get messages from Redis."""
        session_key = f"session:{self.session_id}:messages"
        
        # Get message IDs from sorted set
        min_score = since.timestamp() if since else "-inf"
        message_ids = await self.redis_client.zrangebyscore(
            session_key,
            min_score,
            "+inf",
            start=0,
            num=limit
        )
        
        if not message_ids:
            return []
        
        # Get message data
        messages = []
        for message_id in message_ids:
            key = f"conversation:{self.session_id}:{message_id}"
            data = await self.redis_client.get(key)
            
            if data:
                if self.settings.memory.memory_compression and len(data) % 2 == 0:
                    try:
                        data = gzip.decompress(bytes.fromhex(data)).decode()
                    except:
                        pass  # Not compressed
                
                message = json.loads(data)
                messages.append(message)
        
        return sorted(messages, key=lambda x: x['timestamp'])
    
    async def _get_messages_db(
        self,
        limit: int,
        since: Optional[datetime]
    ) -> List[Dict[str, Any]]:
        """Get messages from database."""
        async with self.db_session() as session:
            query = select(ConversationRecord).where(
                ConversationRecord.session_id == self.session_id
            )
            
            if since:
                query = query.where(ConversationRecord.timestamp >= since)
            
            query = query.order_by(ConversationRecord.timestamp.desc()).limit(limit)
            
            result = await session.execute(query)
            records = result.scalars().all()
            
            messages = []
            for record in records:
                content = record.content
                if record.compressed:
                    try:
                        content = gzip.decompress(bytes.fromhex(content)).decode()
                    except:
                        pass  # Decompression failed
                
                message = {
                    "id": record.id,
                    "session_id": record.session_id,
                    "user_id": record.user_id,
                    "role": record.role,
                    "content": content,
                    "timestamp": record.timestamp.isoformat(),
                    "metadata": json.loads(record.metadata) if record.metadata else None
                }
                messages.append(message)
            
            return list(reversed(messages))  # Return in chronological order
    
    def _get_messages_memory(
        self,
        limit: int,
        since: Optional[datetime]
    ) -> List[Dict[str, Any]]:
        """Get messages from memory."""
        if self.session_id not in self.memory_store:
            return []
        
        messages = self.memory_store[self.session_id]
        
        if since:
            messages = [
                msg for msg in messages
                if datetime.fromisoformat(msg['timestamp']) >= since
            ]
        
        return messages[-limit:] if limit else messages
    
    async def clear_history(self) -> bool:
        """Clear conversation history for the current session."""
        try:
            # Clear from Redis
            if self.redis_client:
                session_key = f"session:{self.session_id}:messages"
                message_ids = await self.redis_client.zrange(session_key, 0, -1)
                
                if message_ids:
                    keys = [f"conversation:{self.session_id}:{msg_id}" for msg_id in message_ids]
                    await self.redis_client.delete(*keys)
                    await self.redis_client.delete(session_key)
            
            # Clear from database
            if self.db_session:
                async with self.db_session() as session:
                    await session.execute(
                        delete(ConversationRecord).where(
                            ConversationRecord.session_id == self.session_id
                        )
                    )
                    await session.commit()
            
            # Clear from memory
            if self.session_id in self.memory_store:
                del self.memory_store[self.session_id]
            
            self.logger.info(f"Cleared conversation history for session {self.session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to clear conversation history: {e}")
            return False
    
    async def get_session_summary(self) -> Optional[str]:
        """Generate a summary of the current conversation session."""
        try:
            messages = await self.get_conversation_history()
            if not messages:
                return None
            
            # Simple summary generation
            user_messages = [msg for msg in messages if msg['role'] == 'user']
            assistant_messages = [msg for msg in messages if msg['role'] == 'assistant']
            
            summary = {
                "session_id": self.session_id,
                "user_id": self.user_id,
                "message_count": len(messages),
                "user_message_count": len(user_messages),
                "assistant_message_count": len(assistant_messages),
                "start_time": messages[0]['timestamp'] if messages else None,
                "last_activity": messages[-1]['timestamp'] if messages else None,
                "duration_minutes": None
            }
            
            if len(messages) >= 2:
                start = datetime.fromisoformat(messages[0]['timestamp'])
                end = datetime.fromisoformat(messages[-1]['timestamp'])
                summary["duration_minutes"] = (end - start).total_seconds() / 60
            
            return json.dumps(summary, indent=2)
            
        except Exception as e:
            self.logger.error(f"Failed to generate session summary: {e}")
            return None
    
    async def health_check(self) -> bool:
        """Perform health check on memory system."""
        try:
            if self.redis_client:
                await self.redis_client.ping()
            
            if self.db_session:
                async with self.db_session() as session:
                    await session.execute(select(1))
            
            return True
            
        except Exception as e:
            self.logger.error(f"Memory health check failed: {e}")
            return False
    
    def get_memory_tools(self) -> List:
        """Get memory-related tools for the agent."""
        from agno.tools import Toolkit
        
        class MemoryTools(Toolkit):
            def __init__(self, memory_instance):
                super().__init__(name="memory_tools")
                self.memory = memory_instance
                self.register(self.get_conversation_history)
                self.register(self.clear_conversation_history)
                self.register(self.get_session_summary)
            
            async def get_conversation_history(self, limit: int = 10) -> str:
                """Get recent conversation history."""
                messages = await self.memory.get_conversation_history(limit=limit)
                return json.dumps(messages, indent=2)
            
            async def clear_conversation_history(self) -> str:
                """Clear conversation history."""
                success = await self.memory.clear_history()
                return json.dumps({"success": success})
            
            async def get_session_summary(self) -> str:
                """Get session summary."""
                summary = await self.memory.get_session_summary()
                return summary or json.dumps({"error": "No session data available"})
        
        return [MemoryTools(self)]
