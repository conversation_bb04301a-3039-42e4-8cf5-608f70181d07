{"timestamp": "2025-07-14T18:20:28.565610", "level": "INFO", "logger": "cache.agent_cache", "message": "Redis cache backend initialized", "module": "agent_cache", "function": "_initialize_backends", "line": 66}
{"timestamp": "2025-07-14T18:20:29.137613", "level": "INFO", "logger": "cache.agent_cache", "message": "Disk cache backend initialized", "module": "agent_cache", "function": "_initialize_backends", "line": 79}
{"timestamp": "2025-07-14T18:20:29.147610", "level": "INFO", "logger": "cache.agent_cache", "message": "Cache system initialized with multi-level backends", "module": "agent_cache", "function": "_initialize_backends", "line": 83}
{"timestamp": "2025-07-14T18:20:30.904534", "level": "INFO", "logger": "cache.agent_cache", "message": "Redis cache backend initialized", "module": "agent_cache", "function": "_initialize_backends", "line": 66}
{"timestamp": "2025-07-14T18:20:31.029531", "level": "INFO", "logger": "cache.agent_cache", "message": "Disk cache backend initialized", "module": "agent_cache", "function": "_initialize_backends", "line": 79}
{"timestamp": "2025-07-14T18:20:31.036534", "level": "INFO", "logger": "cache.agent_cache", "message": "Cache system initialized with multi-level backends", "module": "agent_cache", "function": "_initialize_backends", "line": 83}
{"timestamp": "2025-07-14T18:21:02.162793", "level": "INFO", "logger": "cache.agent_cache", "message": "Redis cache backend initialized", "module": "agent_cache", "function": "_initialize_backends", "line": 66}
{"timestamp": "2025-07-14T18:21:02.300789", "level": "INFO", "logger": "cache.agent_cache", "message": "Disk cache backend initialized", "module": "agent_cache", "function": "_initialize_backends", "line": 79}
{"timestamp": "2025-07-14T18:21:02.307796", "level": "INFO", "logger": "cache.agent_cache", "message": "Cache system initialized with multi-level backends", "module": "agent_cache", "function": "_initialize_backends", "line": 83}
{"timestamp": "2025-07-14T18:21:04.019663", "level": "INFO", "logger": "agents.euri_agent", "message": "EuriAgent initialized with ID: None", "module": "euri_agent", "function": "__init__", "line": 177}
{"timestamp": "2025-07-14T18:21:04.034665", "level": "INFO", "logger": "cache.agent_cache", "message": "Redis cache backend initialized", "module": "agent_cache", "function": "_initialize_backends", "line": 66}
{"timestamp": "2025-07-14T18:21:04.144663", "level": "INFO", "logger": "cache.agent_cache", "message": "Disk cache backend initialized", "module": "agent_cache", "function": "_initialize_backends", "line": 79}
{"timestamp": "2025-07-14T18:21:04.152665", "level": "INFO", "logger": "cache.agent_cache", "message": "Cache system initialized with multi-level backends", "module": "agent_cache", "function": "_initialize_backends", "line": 83}
{"timestamp": "2025-07-14T18:21:05.884523", "level": "INFO", "logger": "agents.euri_agent", "message": "EuriAgent initialized with ID: None", "module": "euri_agent", "function": "__init__", "line": 177}
{"timestamp": "2025-07-14T18:21:09.967736", "level": "WARNING", "logger": "cache.agent_cache", "message": "Redis cache error: Error 22 connecting to localhost:6379. The remote computer refused the network connection.", "module": "agent_cache", "function": "get", "line": 204}
{"timestamp": "2025-07-14T18:21:09.974743", "level": "INFO", "logger": "agents.euri_agent", "message": "Processing message: Hello, how are you?...", "module": "euri_agent", "function": "run_async", "line": 329}
{"timestamp": "2025-07-14T18:21:10.047739", "level": "WARNING", "logger": "utils.error_handler", "message": "Medium severity error: cannot pickle '_thread.RLock' object", "module": "error_handler", "function": "_log_error", "line": 244, "error_category": "unknown_error", "error_severity": "medium", "error_details": {"error_type": "TypeError", "context": null}, "context": null, "user_message": "Hello, how are you?", "traceback": "Traceback (most recent call last):\n  File \"C:\\Euron\\kdhs\\agno-euri-project\\src\\agents\\euri_agent.py\", line 330, in run_async\n    response = await self.agent.arun(\n               ^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\agno\\agent\\agent.py\", line 1318, in arun\n    self.initialize_agent()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\agno\\agent\\agent.py\", line 662, in initialize_agent\n    self.memory.set_model(self.model)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\agno\\memory\\v2\\memory.py\", line 165, in set_model\n    self.memory_manager = MemoryManager(model=deepcopy(model))\n                                              ^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 172, in deepcopy\n    y = _reconstruct(x, memo, *rv)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 271, in _reconstruct\n    state = deepcopy(state, memo)\n            ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 146, in deepcopy\n    y = copier(x, memo)\n        ^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 231, in _deepcopy_dict\n    y[deepcopy(key, memo)] = deepcopy(value, memo)\n                             ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 172, in deepcopy\n    y = _reconstruct(x, memo, *rv)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 271, in _reconstruct\n    state = deepcopy(state, memo)\n            ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 146, in deepcopy\n    y = copier(x, memo)\n        ^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 231, in _deepcopy_dict\n    y[deepcopy(key, memo)] = deepcopy(value, memo)\n                             ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 172, in deepcopy\n    y = _reconstruct(x, memo, *rv)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 271, in _reconstruct\n    state = deepcopy(state, memo)\n            ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 146, in deepcopy\n    y = copier(x, memo)\n        ^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 231, in _deepcopy_dict\n    y[deepcopy(key, memo)] = deepcopy(value, memo)\n                             ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 172, in deepcopy\n    y = _reconstruct(x, memo, *rv)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 271, in _reconstruct\n    state = deepcopy(state, memo)\n            ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 146, in deepcopy\n    y = copier(x, memo)\n        ^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 231, in _deepcopy_dict\n    y[deepcopy(key, memo)] = deepcopy(value, memo)\n                             ^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py\", line 161, in deepcopy\n    rv = reductor(4)\n         ^^^^^^^^^^^\nTypeError: cannot pickle '_thread.RLock' object\n"}
{"timestamp": "2025-07-14T18:21:10.058740", "level": "ERROR", "logger": "agents.euri_agent", "message": "Agent error: cannot pickle '_thread.RLock' object", "module": "euri_agent", "function": "run_async", "line": 357}
