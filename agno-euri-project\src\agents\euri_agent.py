"""
Core Agno agent implementation with euri-client integration.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Union, AsyncIterator
from datetime import datetime

from agno.agent import Agent, AgentMemory, Storage
from agno.tools import Toolkit

try:
    from ..config.settings import get_settings
    from ..tools.euri_tools import EuriTools
    # from ..memory.conversation_memory import ConversationMemory
    from ..cache.agent_cache import AgentCache
    from ..utils.error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
    from ..utils.logger import get_logger
except ImportError:
    # Fallback for direct execution
    from config.settings import get_settings
    from tools.euri_tools import EuriTools
    # from memory.conversation_memory import ConversationMemory
    from cache.agent_cache import AgentCache
    from utils.error_handler import ErrorHandler
    from utils.logger import get_logger


class EuriModel:
    """
    Custom Euri model that integrates with Agno framework.
    Uses euri API as the underlying AI model provider.
    """

    def __init__(
        self,
        api_key: str,
        base_url: str = "https://api.euri.com/v1",
        model_name: str = "euri-chat",
        **kwargs
    ):
        """Initialize Euri model."""
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.model_name = model_name
        self.logger = get_logger(__name__)

        # Initialize HTTP client for euri API
        import httpx
        self.client = httpx.AsyncClient(
            timeout=30,
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
                "User-Agent": "agno-euri-client/1.0.0"
            }
        )

    async def arun(self, message: str, **kwargs) -> Any:
        """
        Run the model asynchronously using euri API.

        Args:
            message: Input message
            **kwargs: Additional arguments

        Returns:
            Model response
        """
        try:
            # Prepare the request payload for euri API
            payload = {
                "model": self.model_name,
                "messages": [{"role": "user", "content": message}],
                "max_tokens": kwargs.get("max_tokens", 1000),
                "temperature": kwargs.get("temperature", 0.7),
                "stream": kwargs.get("stream", False)
            }

            # Make request to euri API
            response = await self.client.post(
                f"{self.base_url}/chat/completions",
                json=payload
            )
            response.raise_for_status()

            result = response.json()

            # Extract the response content
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]

                # Create a response object that mimics Agno's expected format
                class EuriResponse:
                    def __init__(self, content):
                        self.content = content

                    def __str__(self):
                        return self.content

                return EuriResponse(content)
            else:
                raise ValueError("Invalid response format from euri API")

        except Exception as e:
            self.logger.error(f"Euri model error: {e}")
            # Return a fallback response

            class ErrorResponse:
                def __init__(self, error_msg):
                    self.content = f"I apologize, but I encountered an error: {error_msg}. Please try again."

                def __str__(self):
                    return self.content

            return ErrorResponse(str(e))

    def run(self, message: str, **kwargs) -> Any:
        """Synchronous wrapper for arun."""
        import asyncio
        return asyncio.run(self.arun(message, **kwargs))


class EuriAgent:
    """
    Advanced Agno agent with euri-client integration, caching, and memory management.
    """

    def __init__(
        self,
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        custom_instructions: Optional[List[str]] = None,
        **kwargs
    ):
        """
        Initialize the EuriAgent.

        Args:
            agent_id: Unique agent identifier
            user_id: User identifier for session management
            session_id: Session identifier for conversation continuity
            custom_instructions: Additional instructions for the agent
            **kwargs: Additional arguments passed to the Agno Agent
        """
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        self.error_handler = ErrorHandler()

        # Initialize components
        self.cache = AgentCache() if self.settings.features.enable_caching else None
        self.conversation_memory = None  # Temporarily disabled
        # self.conversation_memory = ConversationMemory(
        #     user_id=user_id,
        #     session_id=session_id
        # ) if self.settings.features.enable_memory_persistence else None

        # Initialize AI model
        self.model = self._initialize_model()

        # Initialize tools
        self.tools = self._initialize_tools()

        # Build instructions
        self.instructions = self._build_instructions(custom_instructions)

        # Initialize Agno agent
        self.agent = self._initialize_agent(
            agent_id=agent_id,
            user_id=user_id,
            session_id=session_id,
            **kwargs
        )

        self.logger.info(
            f"EuriAgent initialized with ID: {self.agent.agent_id}")

    def _initialize_model(self):
        """Initialize the Euri AI model."""
        if not self.settings.euri.api_key:
            raise ValueError("Euri API key not configured")

        return EuriModel(
            api_key=self.settings.euri.api_key,
            base_url=self.settings.euri.client_url,
            model_name="euri-chat",  # Default euri model name
            timeout=self.settings.euri.timeout,
            max_retries=self.settings.euri.max_retries
        )

    def _initialize_tools(self) -> List:
        """Initialize agent tools."""
        tools = []

        # Add Euri tools
        euri_tools = EuriTools(
            api_key=self.settings.euri.api_key,
            base_url=self.settings.euri.client_url,
            timeout=self.settings.euri.timeout,
            max_retries=self.settings.euri.max_retries,
            cache=self.cache
        )
        tools.append(euri_tools)

        # Add memory tools if enabled
        if self.conversation_memory:
            tools.extend(self.conversation_memory.get_memory_tools())

        return tools

    def _build_instructions(self, custom_instructions: Optional[List[str]] = None) -> List[str]:
        """Build comprehensive instructions for the agent."""
        base_instructions = [
            "You are an advanced AI assistant powered by the Agno framework with euri-client integration.",
            "You have access to powerful tools for data processing, analysis, and interaction.",
            "Always provide accurate, helpful, and contextually relevant responses.",
            "Use the available tools effectively to enhance your responses.",
            "Maintain conversation context and remember important information across interactions.",
            "If you encounter errors, handle them gracefully and provide helpful feedback.",
            "Be proactive in suggesting relevant actions or information based on the conversation context.",
        ]

        # Add memory-specific instructions
        if self.settings.features.enable_conversation_history:
            base_instructions.extend([
                "Remember important details from our conversation for future reference.",
                "Use conversation history to provide more personalized and contextual responses.",
                "If asked about previous interactions, refer to the conversation memory.",
            ])

        # Add caching instructions
        if self.settings.features.enable_caching:
            base_instructions.extend([
                "Leverage cached information when appropriate to provide faster responses.",
                "Be aware that some information may be cached and might not reflect real-time changes.",
            ])

        # Add custom instructions
        if custom_instructions:
            base_instructions.extend(custom_instructions)

        return base_instructions

    def _initialize_agent(
        self,
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        **kwargs
    ) -> Agent:
        """Initialize the Agno Agent with all configurations."""

        # Set up memory if enabled
        memory = None
        if self.conversation_memory:
            memory = AgentMemory(
                db_url=self.settings.database.database_url,
                create_db=True
            )

        # Set up storage if needed (temporarily disabled)
        storage = None
        # if self.settings.features.enable_memory_persistence:
        #     storage = Storage(
        #         db_url=self.settings.database.database_url,
        #         create_db=True
        #     )

        agent_config = {
            "model": self.model,
            "tools": self.tools,
            "instructions": self.instructions,
            "memory": memory,
            "storage": storage,
            "agent_id": agent_id,
            "user_id": user_id,
            "session_id": session_id,
            "add_history_to_messages": self.settings.features.enable_conversation_history,
            "num_history_runs": self.settings.memory.max_conversation_history,
            "show_tool_calls": self.settings.ai_model.agno_debug,
            "debug_mode": self.settings.ai_model.agno_debug,
            "markdown": True,
            "description": "Advanced AI assistant with euri-client integration and comprehensive memory management",
            "add_datetime_to_instructions": True,
            **kwargs
        }

        return Agent(**agent_config)

    async def run_async(
        self,
        message: str,
        stream: bool = False,
        **kwargs
    ) -> Union[Any, AsyncIterator]:
        """
        Run the agent asynchronously with comprehensive error handling and caching.

        Args:
            message: User message
            stream: Whether to stream the response
            **kwargs: Additional arguments for the agent

        Returns:
            Agent response or async iterator if streaming
        """
        try:
            # Check cache if enabled
            if self.cache and not stream:
                cached_response = await self.cache.get_cached_response(
                    message,
                    self.agent.session_id
                )
                if cached_response:
                    self.logger.info("Returning cached response")
                    return cached_response

            # Store conversation context if memory is enabled
            if self.conversation_memory:
                await self.conversation_memory.add_message(
                    role="user",
                    content=message,
                    timestamp=datetime.utcnow()
                )

            # Run the agent
            self.logger.info(f"Processing message: {message[:100]}...")
            response = await self.agent.arun(
                message=message,
                stream=stream,
                **kwargs
            )

            # Cache the response if not streaming
            if self.cache and not stream:
                await self.cache.cache_response(
                    message,
                    response,
                    self.agent.session_id
                )

            # Store assistant response in memory
            if self.conversation_memory and not stream:
                await self.conversation_memory.add_message(
                    role="assistant",
                    content=str(response.content) if hasattr(
                        response, 'content') else str(response),
                    timestamp=datetime.utcnow()
                )

            return response

        except Exception as e:
            error_response = await self.error_handler.handle_agent_error(e, message)
            self.logger.error(f"Agent error: {e}")
            return error_response

    def run(
        self,
        message: str,
        stream: bool = False,
        **kwargs
    ) -> Any:
        """
        Synchronous wrapper for the async run method.

        Args:
            message: User message
            stream: Whether to stream the response
            **kwargs: Additional arguments for the agent

        Returns:
            Agent response
        """
        return asyncio.run(self.run_async(message, stream, **kwargs))

    async def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get conversation history for the current session."""
        if not self.conversation_memory:
            return []

        return await self.conversation_memory.get_conversation_history()

    async def clear_conversation_history(self) -> bool:
        """Clear conversation history for the current session."""
        if not self.conversation_memory:
            return False

        return await self.conversation_memory.clear_history()

    async def get_session_summary(self) -> Optional[str]:
        """Get a summary of the current session."""
        if not self.conversation_memory:
            return None

        return await self.conversation_memory.get_session_summary()

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about the agent configuration."""
        return {
            "agent_id": self.agent.agent_id,
            "user_id": self.agent.user_id,
            "session_id": self.agent.session_id,
            "model": self.settings.ai_model.agno_model,
            "tools_count": len(self.tools),
            "memory_enabled": self.conversation_memory is not None,
            "cache_enabled": self.cache is not None,
            "created_at": datetime.utcnow().isoformat(),
        }

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check on all agent components."""
        health_status = {
            "agent": "healthy",
            "model": "healthy",
            "tools": "healthy",
            "memory": "healthy" if self.conversation_memory else "disabled",
            "cache": "healthy" if self.cache else "disabled",
            "timestamp": datetime.utcnow().isoformat()
        }

        try:
            # Test model connectivity
            test_response = await self.agent.arun("Hello", stream=False)
            if not test_response:
                health_status["model"] = "unhealthy"
        except Exception as e:
            health_status["model"] = f"unhealthy: {str(e)}"

        # Test cache connectivity
        if self.cache:
            try:
                await self.cache.health_check()
            except Exception as e:
                health_status["cache"] = f"unhealthy: {str(e)}"

        # Test memory connectivity
        if self.conversation_memory:
            try:
                await self.conversation_memory.health_check()
            except Exception as e:
                health_status["memory"] = f"unhealthy: {str(e)}"

        return health_status
