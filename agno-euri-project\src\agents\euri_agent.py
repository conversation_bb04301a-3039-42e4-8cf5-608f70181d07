"""
Core Agno agent implementation with euri-client integration.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Union, AsyncIterator
from datetime import datetime

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import Claude
from agno.memory import AgentMemory
from agno.storage import AgentStorage

from ..config.settings import get_settings
from ..tools.euri_tools import EuriTools
from ..memory.conversation_memory import ConversationMemory
from ..cache.agent_cache import Agent<PERSON>ache
from ..utils.error_handler import <PERSON>rrorHandler
from ..utils.logger import get_logger


class EuriAgent:
    """
    Advanced Agno agent with euri-client integration, caching, and memory management.
    """

    def __init__(
        self,
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        custom_instructions: Optional[List[str]] = None,
        **kwargs
    ):
        """
        Initialize the EuriAgent.

        Args:
            agent_id: Unique agent identifier
            user_id: User identifier for session management
            session_id: Session identifier for conversation continuity
            custom_instructions: Additional instructions for the agent
            **kwargs: Additional arguments passed to the Agno Agent
        """
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        self.error_handler = ErrorHandler()

        # Initialize components
        self.cache = AgentCache() if self.settings.features.enable_caching else None
        self.conversation_memory = ConversationMemory(
            user_id=user_id,
            session_id=session_id
        ) if self.settings.features.enable_memory_persistence else None

        # Initialize AI model
        self.model = self._initialize_model()

        # Initialize tools
        self.tools = self._initialize_tools()

        # Build instructions
        self.instructions = self._build_instructions(custom_instructions)

        # Initialize Agno agent
        self.agent = self._initialize_agent(
            agent_id=agent_id,
            user_id=user_id,
            session_id=session_id,
            **kwargs
        )

        self.logger.info(
            f"EuriAgent initialized with ID: {self.agent.agent_id}")

    def _initialize_model(self):
        """Initialize the AI model based on configuration."""
        model_type = self.settings.ai_model.agno_model.lower()

        if model_type in ["openai", "gpt"]:
            if not self.settings.ai_model.openai_api_key:
                raise ValueError("OpenAI API key not configured")

            return OpenAIChat(
                id=self.settings.ai_model.openai_model,
                api_key=self.settings.ai_model.openai_api_key,
                max_tokens=self.settings.ai_model.openai_max_tokens,
                temperature=self.settings.ai_model.openai_temperature,
            )

        elif model_type in ["anthropic", "claude"]:
            if not self.settings.ai_model.anthropic_api_key:
                raise ValueError("Anthropic API key not configured")

            return Claude(
                id=self.settings.ai_model.anthropic_model,
                api_key=self.settings.ai_model.anthropic_api_key,
            )

        else:
            raise ValueError(f"Unsupported model type: {model_type}")

    def _initialize_tools(self) -> List:
        """Initialize agent tools."""
        tools = []

        # Add Euri tools
        euri_tools = EuriTools(
            api_key=self.settings.euri.api_key,
            base_url=self.settings.euri.client_url,
            timeout=self.settings.euri.timeout,
            max_retries=self.settings.euri.max_retries,
            cache=self.cache
        )
        tools.append(euri_tools)

        # Add memory tools if enabled
        if self.conversation_memory:
            tools.extend(self.conversation_memory.get_memory_tools())

        return tools

    def _build_instructions(self, custom_instructions: Optional[List[str]] = None) -> List[str]:
        """Build comprehensive instructions for the agent."""
        base_instructions = [
            "You are an advanced AI assistant powered by the Agno framework with euri-client integration.",
            "You have access to powerful tools for data processing, analysis, and interaction.",
            "Always provide accurate, helpful, and contextually relevant responses.",
            "Use the available tools effectively to enhance your responses.",
            "Maintain conversation context and remember important information across interactions.",
            "If you encounter errors, handle them gracefully and provide helpful feedback.",
            "Be proactive in suggesting relevant actions or information based on the conversation context.",
        ]

        # Add memory-specific instructions
        if self.settings.features.enable_conversation_history:
            base_instructions.extend([
                "Remember important details from our conversation for future reference.",
                "Use conversation history to provide more personalized and contextual responses.",
                "If asked about previous interactions, refer to the conversation memory.",
            ])

        # Add caching instructions
        if self.settings.features.enable_caching:
            base_instructions.extend([
                "Leverage cached information when appropriate to provide faster responses.",
                "Be aware that some information may be cached and might not reflect real-time changes.",
            ])

        # Add custom instructions
        if custom_instructions:
            base_instructions.extend(custom_instructions)

        return base_instructions

    def _initialize_agent(
        self,
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        **kwargs
    ) -> Agent:
        """Initialize the Agno Agent with all configurations."""

        # Set up memory if enabled
        memory = None
        if self.conversation_memory:
            memory = AgentMemory(
                db_url=self.settings.database.database_url,
                create_db=True
            )

        # Set up storage if needed
        storage = None
        if self.settings.features.enable_memory_persistence:
            storage = AgentStorage(
                db_url=self.settings.database.database_url,
                create_db=True
            )

        agent_config = {
            "model": self.model,
            "tools": self.tools,
            "instructions": self.instructions,
            "memory": memory,
            "storage": storage,
            "agent_id": agent_id,
            "user_id": user_id,
            "session_id": session_id,
            "add_history_to_messages": self.settings.features.enable_conversation_history,
            "num_history_runs": self.settings.memory.max_conversation_history,
            "show_tool_calls": self.settings.ai_model.agno_debug,
            "debug_mode": self.settings.ai_model.agno_debug,
            "markdown": True,
            "description": "Advanced AI assistant with euri-client integration and comprehensive memory management",
            "add_datetime_to_instructions": True,
            **kwargs
        }

        return Agent(**agent_config)

    async def run_async(
        self,
        message: str,
        stream: bool = False,
        **kwargs
    ) -> Union[Any, AsyncIterator]:
        """
        Run the agent asynchronously with comprehensive error handling and caching.

        Args:
            message: User message
            stream: Whether to stream the response
            **kwargs: Additional arguments for the agent

        Returns:
            Agent response or async iterator if streaming
        """
        try:
            # Check cache if enabled
            if self.cache and not stream:
                cached_response = await self.cache.get_cached_response(
                    message,
                    self.agent.session_id
                )
                if cached_response:
                    self.logger.info("Returning cached response")
                    return cached_response

            # Store conversation context if memory is enabled
            if self.conversation_memory:
                await self.conversation_memory.add_message(
                    role="user",
                    content=message,
                    timestamp=datetime.utcnow()
                )

            # Run the agent
            self.logger.info(f"Processing message: {message[:100]}...")
            response = await self.agent.arun(
                message=message,
                stream=stream,
                **kwargs
            )

            # Cache the response if not streaming
            if self.cache and not stream:
                await self.cache.cache_response(
                    message,
                    response,
                    self.agent.session_id
                )

            # Store assistant response in memory
            if self.conversation_memory and not stream:
                await self.conversation_memory.add_message(
                    role="assistant",
                    content=str(response.content) if hasattr(
                        response, 'content') else str(response),
                    timestamp=datetime.utcnow()
                )

            return response

        except Exception as e:
            error_response = await self.error_handler.handle_agent_error(e, message)
            self.logger.error(f"Agent error: {e}")
            return error_response

    def run(
        self,
        message: str,
        stream: bool = False,
        **kwargs
    ) -> Any:
        """
        Synchronous wrapper for the async run method.

        Args:
            message: User message
            stream: Whether to stream the response
            **kwargs: Additional arguments for the agent

        Returns:
            Agent response
        """
        return asyncio.run(self.run_async(message, stream, **kwargs))

    async def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get conversation history for the current session."""
        if not self.conversation_memory:
            return []

        return await self.conversation_memory.get_conversation_history()

    async def clear_conversation_history(self) -> bool:
        """Clear conversation history for the current session."""
        if not self.conversation_memory:
            return False

        return await self.conversation_memory.clear_history()

    async def get_session_summary(self) -> Optional[str]:
        """Get a summary of the current session."""
        if not self.conversation_memory:
            return None

        return await self.conversation_memory.get_session_summary()

    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about the agent configuration."""
        return {
            "agent_id": self.agent.agent_id,
            "user_id": self.agent.user_id,
            "session_id": self.agent.session_id,
            "model": self.settings.ai_model.agno_model,
            "tools_count": len(self.tools),
            "memory_enabled": self.conversation_memory is not None,
            "cache_enabled": self.cache is not None,
            "created_at": datetime.utcnow().isoformat(),
        }

    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check on all agent components."""
        health_status = {
            "agent": "healthy",
            "model": "healthy",
            "tools": "healthy",
            "memory": "healthy" if self.conversation_memory else "disabled",
            "cache": "healthy" if self.cache else "disabled",
            "timestamp": datetime.utcnow().isoformat()
        }

        try:
            # Test model connectivity
            test_response = await self.agent.arun("Hello", stream=False)
            if not test_response:
                health_status["model"] = "unhealthy"
        except Exception as e:
            health_status["model"] = f"unhealthy: {str(e)}"

        # Test cache connectivity
        if self.cache:
            try:
                await self.cache.health_check()
            except Exception as e:
                health_status["cache"] = f"unhealthy: {str(e)}"

        # Test memory connectivity
        if self.conversation_memory:
            try:
                await self.conversation_memory.health_check()
            except Exception as e:
                health_status["memory"] = f"unhealthy: {str(e)}"

        return health_status
