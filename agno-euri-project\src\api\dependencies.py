"""
FastAPI dependencies for the Agno-Euri project.
"""

from typing import Optional, Dict, Any
from fastapi import Depends, HTTPException, Request, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from ..config.settings import get_settings
from ..agents.euri_agent import <PERSON><PERSON><PERSON><PERSON>
from ..utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ..utils.logger import get_logger


# Global instances
_agent_instances: Dict[str, EuriAgent] = {}
_error_handler: Optional[ErrorHandler] = None
_logger = get_logger(__name__)

# Security
security = HTTPBearer(auto_error=False)


def get_error_handler() -> ErrorHandler:
    """Get or create error handler instance."""
    global _error_handler
    if _error_handler is None:
        _error_handler = ErrorHandler()
    return _error_handler


def get_agent(
    request: Request,
    session_id: Optional[str] = Header(None, alias="X-Session-ID"),
    user_id: Optional[str] = Header(None, alias="X-User-ID")
) -> EuriAgent:
    """
    Get or create agent instance for the request.
    
    Args:
        request: FastAPI request object
        session_id: Session ID from header
        user_id: User ID from header
    
    Returns:
        Agent instance
    """
    global _agent_instances
    
    # Determine session ID
    if not session_id:
        # Try to get from query params
        session_id = request.query_params.get("session_id")
    
    if not session_id:
        session_id = "default"
    
    # Get or create agent instance
    if session_id not in _agent_instances:
        try:
            agent = EuriAgent(
                agent_id=session_id,
                user_id=user_id,
                session_id=session_id
            )
            _agent_instances[session_id] = agent
            _logger.info(f"Created new agent instance for session: {session_id}")
        except Exception as e:
            _logger.error(f"Failed to create agent instance: {e}")
            raise HTTPException(
                status_code=500,
                detail="Failed to initialize agent"
            )
    
    return _agent_instances[session_id]


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Optional[Dict[str, Any]]:
    """
    Get current user from authentication token.
    
    Args:
        credentials: HTTP bearer credentials
    
    Returns:
        User information if authenticated
    """
    if not credentials:
        return None
    
    # TODO: Implement proper JWT token validation
    # For now, return a mock user
    return {
        "user_id": "mock_user",
        "username": "test_user",
        "roles": ["user"]
    }


def require_auth(
    user: Optional[Dict[str, Any]] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Require authentication for protected endpoints.
    
    Args:
        user: User information from get_current_user
    
    Returns:
        User information
    
    Raises:
        HTTPException: If user is not authenticated
    """
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"}
        )
    return user


def require_admin(
    user: Dict[str, Any] = Depends(require_auth)
) -> Dict[str, Any]:
    """
    Require admin role for admin endpoints.
    
    Args:
        user: User information from require_auth
    
    Returns:
        User information
    
    Raises:
        HTTPException: If user is not admin
    """
    if "admin" not in user.get("roles", []):
        raise HTTPException(
            status_code=403,
            detail="Admin access required"
        )
    return user


def validate_session_access(
    session_id: str,
    user: Optional[Dict[str, Any]] = Depends(get_current_user)
) -> bool:
    """
    Validate that user has access to the specified session.
    
    Args:
        session_id: Session identifier
        user: User information
    
    Returns:
        True if access is allowed
    
    Raises:
        HTTPException: If access is denied
    """
    # For now, allow access to all sessions
    # TODO: Implement proper session ownership validation
    return True


def get_request_context(request: Request) -> Dict[str, Any]:
    """
    Extract context information from the request.
    
    Args:
        request: FastAPI request object
    
    Returns:
        Request context dictionary
    """
    return {
        "method": request.method,
        "path": request.url.path,
        "query_params": dict(request.query_params),
        "headers": dict(request.headers),
        "client_ip": request.client.host if request.client else None,
        "user_agent": request.headers.get("user-agent"),
        "timestamp": request.state.__dict__.get("start_time")
    }


def validate_content_type(
    request: Request,
    allowed_types: list = ["application/json"]
) -> bool:
    """
    Validate request content type.
    
    Args:
        request: FastAPI request object
        allowed_types: List of allowed content types
    
    Returns:
        True if content type is valid
    
    Raises:
        HTTPException: If content type is invalid
    """
    content_type = request.headers.get("content-type", "").split(";")[0]
    
    if request.method in ["POST", "PUT", "PATCH"] and content_type not in allowed_types:
        raise HTTPException(
            status_code=415,
            detail=f"Unsupported content type. Allowed: {', '.join(allowed_types)}"
        )
    
    return True


def get_pagination_params(
    page: int = 1,
    size: int = 20,
    max_size: int = 100
) -> Dict[str, int]:
    """
    Get and validate pagination parameters.
    
    Args:
        page: Page number (1-based)
        size: Page size
        max_size: Maximum allowed page size
    
    Returns:
        Pagination parameters
    
    Raises:
        HTTPException: If parameters are invalid
    """
    if page < 1:
        raise HTTPException(
            status_code=400,
            detail="Page number must be >= 1"
        )
    
    if size < 1:
        raise HTTPException(
            status_code=400,
            detail="Page size must be >= 1"
        )
    
    if size > max_size:
        raise HTTPException(
            status_code=400,
            detail=f"Page size must be <= {max_size}"
        )
    
    return {
        "page": page,
        "size": size,
        "offset": (page - 1) * size,
        "limit": size
    }


def get_filter_params(
    request: Request,
    allowed_filters: Optional[list] = None
) -> Dict[str, Any]:
    """
    Extract and validate filter parameters from query string.
    
    Args:
        request: FastAPI request object
        allowed_filters: List of allowed filter keys
    
    Returns:
        Filter parameters dictionary
    """
    filters = {}
    
    for key, value in request.query_params.items():
        # Skip pagination and standard params
        if key in ["page", "size", "sort", "order"]:
            continue
        
        # Check if filter is allowed
        if allowed_filters and key not in allowed_filters:
            continue
        
        filters[key] = value
    
    return filters


def get_sort_params(
    sort: Optional[str] = None,
    order: str = "asc",
    allowed_fields: Optional[list] = None
) -> Dict[str, str]:
    """
    Get and validate sort parameters.
    
    Args:
        sort: Field to sort by
        order: Sort order (asc/desc)
        allowed_fields: List of allowed sort fields
    
    Returns:
        Sort parameters
    
    Raises:
        HTTPException: If parameters are invalid
    """
    if order not in ["asc", "desc"]:
        raise HTTPException(
            status_code=400,
            detail="Order must be 'asc' or 'desc'"
        )
    
    if sort and allowed_fields and sort not in allowed_fields:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid sort field. Allowed: {', '.join(allowed_fields)}"
        )
    
    return {
        "sort": sort,
        "order": order
    }


def cleanup_agent_instances():
    """Clean up inactive agent instances."""
    global _agent_instances
    
    # TODO: Implement proper cleanup logic based on last activity
    # For now, just log the count
    _logger.info(f"Active agent instances: {len(_agent_instances)}")


def get_agent_by_id(agent_id: str) -> Optional[EuriAgent]:
    """
    Get agent instance by ID.
    
    Args:
        agent_id: Agent identifier
    
    Returns:
        Agent instance if found
    """
    return _agent_instances.get(agent_id)


def remove_agent_instance(session_id: str) -> bool:
    """
    Remove agent instance.
    
    Args:
        session_id: Session identifier
    
    Returns:
        True if removed successfully
    """
    global _agent_instances
    
    if session_id in _agent_instances:
        del _agent_instances[session_id]
        _logger.info(f"Removed agent instance for session: {session_id}")
        return True
    
    return False


def get_active_sessions() -> list:
    """
    Get list of active session IDs.
    
    Returns:
        List of active session IDs
    """
    return list(_agent_instances.keys())
