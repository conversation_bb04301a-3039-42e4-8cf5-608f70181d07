# Agno-Euri Project

A comprehensive, production-ready AI agent system built with the Agno framework, featuring euri-client integration, advanced caching, and conversational memory management.

## Features

- 🤖 **Advanced AI Agent**: Built with Agno framework for high-performance agent interactions
- 🔗 **Euri Integration**: Seamless integration with euri-client and euri-api_key
- 💾 **Multi-Level Caching**: Redis-based caching for API responses, embeddings, and conversations
- 🧠 **Conversational Memory**: Persistent session management with user context
- 🚀 **FastAPI Backend**: Production-ready API endpoints with async support
- 🎯 **Custom Tools**: Specialized tools for euri-specific functionality
- 📊 **Monitoring**: Built-in logging and performance monitoring
- 🔒 **Security**: Secure API key management and request validation
- 🧪 **Comprehensive Testing**: Full test suite with integration tests
- 📚 **Rich Documentation**: Detailed guides and examples

## Project Structure

```
agno-euri-project/
├── src/
│   ├── agents/              # Agent implementations
│   ├── tools/               # Custom tools and utilities
│   ├── memory/              # Memory and session management
│   ├── cache/               # Caching implementations
│   ├── api/                 # FastAPI routes and endpoints
│   ├── config/              # Configuration management
│   └── utils/               # Helper utilities
├── tests/                   # Test suite
├── docs/                    # Documentation
├── examples/                # Usage examples
├── scripts/                 # Deployment and utility scripts
├── requirements.txt         # Python dependencies
├── docker-compose.yml       # Docker setup
├── .env.example            # Environment variables template
└── README.md               # This file
```

## Quick Start

1. **Clone and Setup**
   ```bash
   cd agno-euri-project
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

3. **Start Redis (for caching)**
   ```bash
   docker-compose up -d redis
   ```

4. **Run the Agent**
   ```bash
   python src/main.py
   ```

5. **Start API Server**
   ```bash
   uvicorn src.api.main:app --reload
   ```

## Configuration

The system uses environment variables for configuration. See `.env.example` for all available options.

Key configurations:
- `EURI_API_KEY`: Your euri API key
- `EURI_CLIENT_URL`: Euri client endpoint URL
- `REDIS_URL`: Redis connection string for caching
- `AGNO_MODEL`: AI model to use (default: gpt-4)

## Usage Examples

See the `examples/` directory for comprehensive usage examples including:
- Basic agent interactions
- Advanced conversational flows
- Custom tool implementations
- API integration patterns

## Testing

Run the test suite:
```bash
pytest tests/ -v
```

## Documentation

Detailed documentation is available in the `docs/` directory:
- [Architecture Overview](docs/architecture.md)
- [API Reference](docs/api.md)
- [Configuration Guide](docs/configuration.md)
- [Deployment Guide](docs/deployment.md)

## License

MIT License - see LICENSE file for details.
