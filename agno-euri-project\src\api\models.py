"""
Pydantic models for API requests and responses.
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator


class ChatRequest(BaseModel):
    """Request model for chat endpoint."""
    
    message: str = Field(..., description="User message", min_length=1, max_length=10000)
    user_id: Optional[str] = Field(None, description="User identifier")
    session_id: Optional[str] = Field(None, description="Session identifier")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")
    options: Optional[Dict[str, Any]] = Field(None, description="Chat options")
    
    @validator("message")
    def validate_message(cls, v):
        if not v.strip():
            raise ValueError("Message cannot be empty")
        return v.strip()


class ChatResponse(BaseModel):
    """Response model for chat endpoint."""
    
    response: str = Field(..., description="Agent response")
    session_id: Optional[str] = Field(None, description="Session identifier")
    user_id: Optional[str] = Field(None, description="User identifier")
    agent_id: Optional[str] = Field(None, description="Agent identifier")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
    error: bool = Field(False, description="Whether an error occurred")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class SessionRequest(BaseModel):
    """Request model for session creation."""
    
    user_id: str = Field(..., description="User identifier")
    session_id: Optional[str] = Field(None, description="Specific session ID (auto-generated if None)")
    custom_instructions: Optional[List[str]] = Field(None, description="Custom instructions for the agent")
    context: Optional[Dict[str, Any]] = Field(None, description="Initial context")


class SessionResponse(BaseModel):
    """Response model for session operations."""
    
    session_id: str = Field(..., description="Session identifier")
    user_id: str = Field(..., description="User identifier")
    agent_id: str = Field(..., description="Agent identifier")
    created: bool = Field(..., description="Whether session was created")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Operation timestamp")


class AgentInfoResponse(BaseModel):
    """Response model for agent information."""
    
    agent_id: str = Field(..., description="Agent identifier")
    user_id: Optional[str] = Field(None, description="User identifier")
    session_id: Optional[str] = Field(None, description="Session identifier")
    model: str = Field(..., description="AI model being used")
    tools_count: int = Field(..., description="Number of tools available")
    memory_enabled: bool = Field(..., description="Whether memory is enabled")
    cache_enabled: bool = Field(..., description="Whether caching is enabled")
    created_at: str = Field(..., description="Agent creation timestamp")


class HealthResponse(BaseModel):
    """Response model for health check."""
    
    status: str = Field(..., description="Health status")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Health check timestamp")
    version: str = Field(..., description="API version")
    environment: str = Field(..., description="Environment name")
    agent_health: Optional[Dict[str, Any]] = Field(None, description="Agent health details")


class CacheStatsResponse(BaseModel):
    """Response model for cache statistics."""
    
    hits: int = Field(..., description="Cache hits")
    misses: int = Field(..., description="Cache misses")
    redis_hits: int = Field(..., description="Redis cache hits")
    disk_hits: int = Field(..., description="Disk cache hits")
    memory_hits: int = Field(..., description="Memory cache hits")
    errors: int = Field(..., description="Cache errors")
    total_requests: int = Field(..., description="Total cache requests")
    hit_rate_percent: float = Field(..., description="Cache hit rate percentage")
    memory_cache_size: int = Field(..., description="Memory cache size")
    backends: Dict[str, bool] = Field(..., description="Available cache backends")
    health: Dict[str, Any] = Field(..., description="Cache health status")


class ErrorResponse(BaseModel):
    """Response model for errors."""
    
    error: bool = Field(True, description="Error flag")
    message: str = Field(..., description="Error message")
    error_type: Optional[str] = Field(None, description="Error type")
    details: Optional[Dict[str, Any]] = Field(None, description="Error details")
    suggestions: Optional[List[str]] = Field(None, description="Suggestions for resolution")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")


class ConversationMessage(BaseModel):
    """Model for conversation messages."""
    
    id: str = Field(..., description="Message identifier")
    role: str = Field(..., description="Message role (user/assistant)")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(..., description="Message timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Message metadata")
    
    @validator("role")
    def validate_role(cls, v):
        if v not in ["user", "assistant", "system"]:
            raise ValueError("Role must be 'user', 'assistant', or 'system'")
        return v


class ConversationHistory(BaseModel):
    """Model for conversation history."""
    
    session_id: str = Field(..., description="Session identifier")
    user_id: str = Field(..., description="User identifier")
    messages: List[ConversationMessage] = Field(..., description="Conversation messages")
    total_messages: int = Field(..., description="Total number of messages")
    start_time: Optional[datetime] = Field(None, description="Conversation start time")
    last_activity: Optional[datetime] = Field(None, description="Last activity time")


class EuriToolRequest(BaseModel):
    """Request model for Euri tool operations."""
    
    tool_name: str = Field(..., description="Name of the Euri tool to use")
    parameters: Dict[str, Any] = Field(..., description="Tool parameters")
    session_id: Optional[str] = Field(None, description="Session identifier")
    cache_result: bool = Field(True, description="Whether to cache the result")


class EuriToolResponse(BaseModel):
    """Response model for Euri tool operations."""
    
    tool_name: str = Field(..., description="Name of the tool used")
    result: Any = Field(..., description="Tool execution result")
    success: bool = Field(..., description="Whether the operation was successful")
    execution_time: Optional[float] = Field(None, description="Execution time in seconds")
    cached: bool = Field(False, description="Whether result was cached")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
    error: Optional[str] = Field(None, description="Error message if failed")


class BatchChatRequest(BaseModel):
    """Request model for batch chat processing."""
    
    messages: List[str] = Field(..., description="List of messages to process", min_items=1, max_items=10)
    user_id: Optional[str] = Field(None, description="User identifier")
    session_id: Optional[str] = Field(None, description="Session identifier")
    parallel: bool = Field(False, description="Whether to process messages in parallel")
    context: Optional[Dict[str, Any]] = Field(None, description="Shared context for all messages")


class BatchChatResponse(BaseModel):
    """Response model for batch chat processing."""
    
    responses: List[ChatResponse] = Field(..., description="List of chat responses")
    total_messages: int = Field(..., description="Total number of messages processed")
    successful: int = Field(..., description="Number of successful responses")
    failed: int = Field(..., description="Number of failed responses")
    processing_time: float = Field(..., description="Total processing time in seconds")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Batch completion timestamp")


class SystemStats(BaseModel):
    """Model for system statistics."""
    
    active_sessions: int = Field(..., description="Number of active sessions")
    total_requests: int = Field(..., description="Total number of requests processed")
    cache_stats: Optional[CacheStatsResponse] = Field(None, description="Cache statistics")
    error_stats: Optional[Dict[str, Any]] = Field(None, description="Error statistics")
    uptime_seconds: float = Field(..., description="System uptime in seconds")
    memory_usage: Optional[Dict[str, Any]] = Field(None, description="Memory usage statistics")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Statistics timestamp")


class ConfigurationResponse(BaseModel):
    """Response model for configuration information."""
    
    environment: str = Field(..., description="Environment name")
    debug_mode: bool = Field(..., description="Debug mode status")
    features: Dict[str, bool] = Field(..., description="Feature flags")
    model_config: Dict[str, Any] = Field(..., description="AI model configuration")
    cache_config: Dict[str, Any] = Field(..., description="Cache configuration")
    api_config: Dict[str, Any] = Field(..., description="API configuration")


class WebhookRequest(BaseModel):
    """Request model for webhook endpoints."""
    
    event_type: str = Field(..., description="Type of webhook event")
    payload: Dict[str, Any] = Field(..., description="Webhook payload")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Event timestamp")
    signature: Optional[str] = Field(None, description="Webhook signature for verification")


class WebhookResponse(BaseModel):
    """Response model for webhook endpoints."""
    
    received: bool = Field(True, description="Whether webhook was received")
    processed: bool = Field(..., description="Whether webhook was processed successfully")
    message: str = Field(..., description="Processing message")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
