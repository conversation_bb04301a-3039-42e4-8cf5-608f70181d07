# Core Agno Framework
agno>=1.7.2

# AI Models and APIs
openai>=1.0.0
anthropic>=0.25.0

# Web Framework and API
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Caching and Storage
redis>=5.0.0
hiredis>=2.2.0
diskcache>=5.6.0

# Database and ORM
sqlalchemy>=2.0.0
alembic>=1.13.0
asyncpg>=0.29.0  # For PostgreSQL async support
aiosqlite>=0.19.0  # For SQLite async support

# HTTP Client for euri integration
httpx>=0.25.0
aiohttp>=3.9.0

# Memory and Session Management
pickle5>=0.0.12
joblib>=1.3.0

# Utilities and Helpers
python-dotenv>=1.0.0
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
email-validator>=2.1.0

# Logging and Monitoring
structlog>=23.2.0
rich>=13.7.0
prometheus-client>=0.19.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
httpx>=0.25.0  # For testing FastAPI endpoints

# Development Tools
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.0
pre-commit>=3.6.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.4.0
mkdocs-mermaid2-plugin>=1.1.0

# Security
cryptography>=41.0.0
bcrypt>=4.1.0

# Performance and Optimization
orjson>=3.9.0  # Fast JSON serialization
msgpack>=1.0.0  # Binary serialization

# Optional: Vector Database Support
chromadb>=0.4.0
pinecone-client>=3.0.0
weaviate-client>=4.4.0

# Optional: Additional Tools
beautifulsoup4>=4.12.0
requests>=2.31.0
pandas>=2.1.0
numpy>=1.24.0
