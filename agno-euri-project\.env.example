# =============================================================================
# AGNO-EURI PROJECT ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual values

# =============================================================================
# EURI API CONFIGURATION
# =============================================================================
EURI_API_KEY=demo_euri_key_for_testing
EURI_CLIENT_URL=https://api.euri.com/v1
EURI_TIMEOUT=30
EURI_MAX_RETRIES=3
EURI_RETRY_DELAY=1

# =============================================================================
# AI MODEL CONFIGURATION
# =============================================================================
# Primary AI Model (OpenAI)
OPENAI_API_KEY=demo_openai_key_for_testing
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_MAX_TOKENS=4096
OPENAI_TEMPERATURE=0.7

# Alternative AI Model (Anthropic)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# =============================================================================
# AGNO FRAMEWORK CONFIGURATION
# =============================================================================
AGNO_MODEL=openai
AGNO_TELEMETRY=false
AGNO_DEBUG=false
AGNO_LOG_LEVEL=INFO

# =============================================================================
# CACHING CONFIGURATION
# =============================================================================
# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_MAX_CONNECTIONS=20
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# Cache Settings
CACHE_TTL_DEFAULT=3600  # 1 hour
CACHE_TTL_CONVERSATIONS=86400  # 24 hours
CACHE_TTL_EMBEDDINGS=604800  # 7 days
CACHE_TTL_API_RESPONSES=1800  # 30 minutes
CACHE_MAX_SIZE=1000  # Maximum number of items in memory cache

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# SQLite (default for development)
DATABASE_URL=sqlite+aiosqlite:///./data/agno_euri.db

# PostgreSQL (for production)
# DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/agno_euri

# =============================================================================
# API SERVER CONFIGURATION
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true
API_WORKERS=1
API_LOG_LEVEL=info

# Security
SECRET_KEY=your_super_secret_key_here_change_this_in_production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]

# =============================================================================
# MEMORY AND SESSION CONFIGURATION
# =============================================================================
# Session Management
SESSION_TIMEOUT=3600  # 1 hour
MAX_CONVERSATION_HISTORY=50
MAX_SESSION_MEMORY_SIZE=10485760  # 10MB

# Memory Storage
MEMORY_BACKEND=redis  # Options: redis, sqlite, memory
MEMORY_COMPRESSION=true
MEMORY_ENCRYPTION=false

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/agno_euri.log
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5

# =============================================================================
# MONITORING AND METRICS
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
ENVIRONMENT=development  # Options: development, staging, production
DEBUG=true
TESTING=false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds

# Request Validation
MAX_REQUEST_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=["txt", "pdf", "docx", "md"]

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_CACHING=true
ENABLE_MEMORY_PERSISTENCE=true
ENABLE_CONVERSATION_HISTORY=true
ENABLE_EMBEDDINGS_CACHE=true
ENABLE_API_RESPONSE_CACHE=true
ENABLE_METRICS_COLLECTION=true
ENABLE_DETAILED_LOGGING=true

# =============================================================================
# EXTERNAL SERVICES (OPTIONAL)
# =============================================================================
# Vector Database (if using)
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment_here

# Monitoring Services
SENTRY_DSN=your_sentry_dsn_here
DATADOG_API_KEY=your_datadog_api_key_here
