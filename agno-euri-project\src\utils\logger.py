"""
Advanced logging configuration for the Agno-Euri project.
"""

import logging
import logging.handlers
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

import structlog
from rich.logging import <PERSON>Handler
from rich.console import Console

try:
    from ..config.settings import get_settings
except ImportError:
    from config.settings import get_settings


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""

    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)

        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ["name", "msg", "args", "levelname", "levelno", "pathname",
                           "filename", "module", "lineno", "funcName", "created",
                           "msecs", "relativeCreated", "thread", "threadName",
                           "processName", "process", "getMessage", "exc_info",
                           "exc_text", "stack_info"]:
                log_entry[key] = value

        return json.dumps(log_entry, default=str)


class ContextFilter(logging.Filter):
    """Add context information to log records."""

    def __init__(self, context: Optional[Dict[str, Any]] = None):
        super().__init__()
        self.context = context or {}

    def filter(self, record: logging.LogRecord) -> bool:
        """Add context to log record."""
        for key, value in self.context.items():
            setattr(record, key, value)
        return True


def setup_logging() -> None:
    """Setup comprehensive logging configuration."""
    settings = get_settings()

    # Create logs directory
    log_path = Path(settings.logging.log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.logging.log_level.upper()))

    # Clear existing handlers
    root_logger.handlers.clear()

    # Console handler with Rich formatting
    console_handler = RichHandler(
        console=Console(stderr=True),
        show_time=True,
        show_path=True,
        markup=True,
        rich_tracebacks=True,
        tracebacks_show_locals=settings.debug
    )
    console_handler.setLevel(
        getattr(logging, settings.logging.log_level.upper()))

    if settings.logging.log_format.lower() == "json":
        console_handler.setFormatter(JSONFormatter())
    else:
        console_handler.setFormatter(
            logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
        )

    root_logger.addHandler(console_handler)

    # File handler with rotation
    if settings.logging.log_file:
        file_handler = logging.handlers.RotatingFileHandler(
            settings.logging.log_file,
            maxBytes=settings.logging.log_max_size,
            backupCount=settings.logging.log_backup_count,
            encoding="utf-8"
        )
        file_handler.setLevel(logging.DEBUG)  # Always debug level for files

        if settings.logging.log_format.lower() == "json":
            file_handler.setFormatter(JSONFormatter())
        else:
            file_handler.setFormatter(
                logging.Formatter(
                    "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
                )
            )

        root_logger.addHandler(file_handler)

    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.logging.log_format.lower() == "json"
            else structlog.dev.ConsoleRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Set specific logger levels
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("redis").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy").setLevel(logging.WARNING)

    # Enable detailed logging if configured
    if settings.features.enable_detailed_logging:
        logging.getLogger("agno").setLevel(logging.DEBUG)
        logging.getLogger("src").setLevel(logging.DEBUG)


def get_logger(name: str, context: Optional[Dict[str, Any]] = None) -> logging.Logger:
    """
    Get a configured logger instance.

    Args:
        name: Logger name
        context: Additional context to include in logs

    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)

    if context:
        # Add context filter
        context_filter = ContextFilter(context)
        logger.addFilter(context_filter)

    return logger


def get_structured_logger(name: str, **context) -> structlog.BoundLogger:
    """
    Get a structured logger with context.

    Args:
        name: Logger name
        **context: Context to bind to logger

    Returns:
        Structured logger with bound context
    """
    logger = structlog.get_logger(name)
    if context:
        logger = logger.bind(**context)
    return logger


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""

    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class."""
        return get_logger(self.__class__.__module__ + "." + self.__class__.__name__)

    @property
    def structured_logger(self) -> structlog.BoundLogger:
        """Get structured logger for this class."""
        return get_structured_logger(
            self.__class__.__module__ + "." + self.__class__.__name__,
            class_name=self.__class__.__name__
        )


def log_function_call(func):
    """Decorator to log function calls with arguments and results."""
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        logger.debug(
            f"Calling {func.__name__} with args={args}, kwargs={kwargs}")

        try:
            result = func(*args, **kwargs)
            logger.debug(f"{func.__name__} completed successfully")
            return result
        except Exception as e:
            logger.error(f"{func.__name__} failed with error: {e}")
            raise

    return wrapper


async def log_async_function_call(func):
    """Decorator to log async function calls with arguments and results."""
    async def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        logger.debug(
            f"Calling async {func.__name__} with args={args}, kwargs={kwargs}")

        try:
            result = await func(*args, **kwargs)
            logger.debug(f"Async {func.__name__} completed successfully")
            return result
        except Exception as e:
            logger.error(f"Async {func.__name__} failed with error: {e}")
            raise

    return wrapper


class PerformanceLogger:
    """Context manager for performance logging."""

    def __init__(self, operation_name: str, logger: Optional[logging.Logger] = None):
        self.operation_name = operation_name
        self.logger = logger or get_logger(__name__)
        self.start_time = None

    def __enter__(self):
        self.start_time = datetime.utcnow()
        self.logger.debug(f"Starting operation: {self.operation_name}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.utcnow()
        duration = (end_time - self.start_time).total_seconds()

        if exc_type:
            self.logger.error(
                f"Operation {self.operation_name} failed after {duration:.3f}s: {exc_val}"
            )
        else:
            self.logger.info(
                f"Operation {self.operation_name} completed in {duration:.3f}s"
            )


# Initialize logging when module is imported
if not logging.getLogger().handlers:
    setup_logging()
