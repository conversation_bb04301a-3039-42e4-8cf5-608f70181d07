#!/usr/bin/env python3
"""
Basic test script to verify the Agno-Euri project setup.
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test basic imports."""
    print("Testing imports...")
    
    try:
        from config.settings import Settings
        print("✅ Settings import successful")
    except Exception as e:
        print(f"❌ Settings import failed: {e}")
        return False
    
    try:
        from agents.euri_agent import EuriAgent, EuriModel
        print("✅ EuriAgent import successful")
    except Exception as e:
        print(f"❌ EuriAgent import failed: {e}")
        return False
    
    return True

def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        from config.settings import Settings
        settings = Settings()
        print("✅ Configuration loaded successfully")
        print(f"   Environment: {settings.environment}")
        print(f"   Euri API Key: {settings.euri.api_key[:10]}...")
        print(f"   Model: {settings.ai_model.agno_model}")
        return True
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        return False

def test_euri_model():
    """Test Euri model creation."""
    print("\nTesting Euri model...")
    
    try:
        from agents.euri_agent import EuriModel
        model = EuriModel(api_key="test_key")
        print("✅ EuriModel created successfully")
        return True
    except Exception as e:
        print(f"❌ EuriModel creation failed: {e}")
        return False

def test_agent_creation():
    """Test agent creation."""
    print("\nTesting agent creation...")
    
    try:
        from agents.euri_agent import EuriAgent
        agent = EuriAgent(user_id="test_user", session_id="test_session")
        print("✅ EuriAgent created successfully")
        print(f"   Agent ID: {agent.agent.agent_id}")
        print(f"   User ID: {agent.agent.user_id}")
        print(f"   Session ID: {agent.agent.session_id}")
        return True
    except Exception as e:
        print(f"❌ EuriAgent creation failed: {e}")
        return False

async def test_agent_response():
    """Test agent response."""
    print("\nTesting agent response...")
    
    try:
        from agents.euri_agent import EuriAgent
        agent = EuriAgent(user_id="test_user", session_id="test_session")
        
        response = await agent.run_async("Hello, how are you?")
        print("✅ Agent response successful")
        print(f"   Response: {str(response)[:100]}...")
        return True
    except Exception as e:
        print(f"❌ Agent response failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Agno-Euri Basic Tests")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_configuration,
        test_euri_model,
        test_agent_creation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    # Test async function
    try:
        import asyncio
        if asyncio.run(test_agent_response()):
            passed += 1
        total += 1
    except Exception as e:
        print(f"❌ Async test failed: {e}")
        total += 1
    
    print("\n" + "=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
