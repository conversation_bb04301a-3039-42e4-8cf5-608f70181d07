"""
Comprehensive error handling utilities for the Agno-Euri project.
"""

import traceback
import sys
from typing import Dict, Any, Optional, Type, Union, Callable
from datetime import datetime
from enum import Enum

from ..config.settings import get_settings
from .logger import get_logger


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification."""
    API_ERROR = "api_error"
    NETWORK_ERROR = "network_error"
    AUTHENTICATION_ERROR = "authentication_error"
    VALIDATION_ERROR = "validation_error"
    CONFIGURATION_ERROR = "configuration_error"
    CACHE_ERROR = "cache_error"
    MEMORY_ERROR = "memory_error"
    AGENT_ERROR = "agent_error"
    TOOL_ERROR = "tool_error"
    UNKNOWN_ERROR = "unknown_error"


class AgnoEuriError(Exception):
    """Base exception class for Agno-Euri project."""
    
    def __init__(
        self,
        message: str,
        category: ErrorCategory = ErrorCategory.UNKNOWN_ERROR,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.details = details or {}
        self.original_exception = original_exception
        self.timestamp = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary representation."""
        return {
            "message": self.message,
            "category": self.category.value,
            "severity": self.severity.value,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "original_exception": str(self.original_exception) if self.original_exception else None
        }


class EuriAPIError(AgnoEuriError):
    """Error related to Euri API operations."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, **kwargs):
        super().__init__(message, category=ErrorCategory.API_ERROR, **kwargs)
        self.status_code = status_code
        if status_code:
            self.details["status_code"] = status_code


class CacheError(AgnoEuriError):
    """Error related to caching operations."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.CACHE_ERROR, **kwargs)


class MemoryError(AgnoEuriError):
    """Error related to memory operations."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.MEMORY_ERROR, **kwargs)


class AgentError(AgnoEuriError):
    """Error related to agent operations."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.AGENT_ERROR, **kwargs)


class ErrorHandler:
    """Comprehensive error handling and recovery system."""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        self.error_counts = {}
        self.recovery_strategies = {}
        
        # Register default recovery strategies
        self._register_default_strategies()
    
    def _register_default_strategies(self):
        """Register default error recovery strategies."""
        self.recovery_strategies.update({
            ErrorCategory.API_ERROR: self._handle_api_error,
            ErrorCategory.NETWORK_ERROR: self._handle_network_error,
            ErrorCategory.CACHE_ERROR: self._handle_cache_error,
            ErrorCategory.MEMORY_ERROR: self._handle_memory_error,
            ErrorCategory.AGENT_ERROR: self._handle_agent_error,
        })
    
    async def handle_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Handle an error with appropriate recovery strategy.
        
        Args:
            error: The exception that occurred
            context: Additional context about the error
            user_message: Original user message that caused the error
        
        Returns:
            Error response with recovery information
        """
        # Convert to AgnoEuriError if needed
        if not isinstance(error, AgnoEuriError):
            agno_error = self._classify_error(error, context)
        else:
            agno_error = error
        
        # Log the error
        self._log_error(agno_error, context, user_message)
        
        # Update error statistics
        self._update_error_stats(agno_error)
        
        # Apply recovery strategy
        recovery_response = await self._apply_recovery_strategy(agno_error, context, user_message)
        
        return {
            "error": agno_error.to_dict(),
            "recovery": recovery_response,
            "user_friendly_message": self._generate_user_friendly_message(agno_error),
            "suggestions": self._generate_suggestions(agno_error),
            "retry_possible": recovery_response.get("retry_possible", False)
        }
    
    def _classify_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> AgnoEuriError:
        """Classify an unknown error into appropriate category."""
        error_str = str(error).lower()
        error_type = type(error).__name__
        
        # API-related errors
        if any(keyword in error_str for keyword in ["api", "http", "request", "response", "status"]):
            return EuriAPIError(
                str(error),
                original_exception=error,
                details={"error_type": error_type, "context": context}
            )
        
        # Network-related errors
        if any(keyword in error_str for keyword in ["connection", "timeout", "network", "dns"]):
            return AgnoEuriError(
                str(error),
                category=ErrorCategory.NETWORK_ERROR,
                original_exception=error,
                details={"error_type": error_type, "context": context}
            )
        
        # Authentication errors
        if any(keyword in error_str for keyword in ["auth", "token", "key", "permission", "unauthorized"]):
            return AgnoEuriError(
                str(error),
                category=ErrorCategory.AUTHENTICATION_ERROR,
                severity=ErrorSeverity.HIGH,
                original_exception=error,
                details={"error_type": error_type, "context": context}
            )
        
        # Cache errors
        if any(keyword in error_str for keyword in ["cache", "redis", "disk"]):
            return CacheError(
                str(error),
                original_exception=error,
                details={"error_type": error_type, "context": context}
            )
        
        # Memory errors
        if any(keyword in error_str for keyword in ["memory", "session", "conversation"]):
            return MemoryError(
                str(error),
                original_exception=error,
                details={"error_type": error_type, "context": context}
            )
        
        # Default to unknown error
        return AgnoEuriError(
            str(error),
            category=ErrorCategory.UNKNOWN_ERROR,
            original_exception=error,
            details={"error_type": error_type, "context": context}
        )
    
    def _log_error(
        self,
        error: AgnoEuriError,
        context: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ):
        """Log error with appropriate level based on severity."""
        log_data = {
            "error_category": error.category.value,
            "error_severity": error.severity.value,
            "error_details": error.details,
            "context": context,
            "user_message": user_message,
            "traceback": traceback.format_exc() if error.original_exception else None
        }
        
        if error.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"Critical error: {error.message}", extra=log_data)
        elif error.severity == ErrorSeverity.HIGH:
            self.logger.error(f"High severity error: {error.message}", extra=log_data)
        elif error.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(f"Medium severity error: {error.message}", extra=log_data)
        else:
            self.logger.info(f"Low severity error: {error.message}", extra=log_data)
    
    def _update_error_stats(self, error: AgnoEuriError):
        """Update error statistics for monitoring."""
        category_key = error.category.value
        if category_key not in self.error_counts:
            self.error_counts[category_key] = 0
        self.error_counts[category_key] += 1
    
    async def _apply_recovery_strategy(
        self,
        error: AgnoEuriError,
        context: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """Apply appropriate recovery strategy for the error."""
        strategy = self.recovery_strategies.get(error.category)
        
        if strategy:
            try:
                return await strategy(error, context, user_message)
            except Exception as e:
                self.logger.error(f"Recovery strategy failed: {e}")
                return {"status": "recovery_failed", "retry_possible": False}
        
        return {"status": "no_recovery_strategy", "retry_possible": False}
    
    async def _handle_api_error(
        self,
        error: AgnoEuriError,
        context: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle API-related errors."""
        if isinstance(error, EuriAPIError) and error.status_code:
            if error.status_code == 429:  # Rate limited
                return {
                    "status": "rate_limited",
                    "retry_possible": True,
                    "retry_after": 60,
                    "message": "API rate limit exceeded. Please try again in a minute."
                }
            elif error.status_code == 401:  # Unauthorized
                return {
                    "status": "authentication_failed",
                    "retry_possible": False,
                    "message": "API authentication failed. Please check your API key."
                }
            elif error.status_code >= 500:  # Server error
                return {
                    "status": "server_error",
                    "retry_possible": True,
                    "retry_after": 30,
                    "message": "Server error occurred. Retrying may help."
                }
        
        return {
            "status": "api_error",
            "retry_possible": True,
            "message": "API request failed. Please try again."
        }
    
    async def _handle_network_error(
        self,
        error: AgnoEuriError,
        context: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle network-related errors."""
        return {
            "status": "network_error",
            "retry_possible": True,
            "retry_after": 10,
            "message": "Network connection issue. Please check your internet connection and try again."
        }
    
    async def _handle_cache_error(
        self,
        error: AgnoEuriError,
        context: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle cache-related errors."""
        return {
            "status": "cache_error",
            "retry_possible": True,
            "message": "Caching system issue. Continuing without cache.",
            "fallback_mode": True
        }
    
    async def _handle_memory_error(
        self,
        error: AgnoEuriError,
        context: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle memory-related errors."""
        return {
            "status": "memory_error",
            "retry_possible": True,
            "message": "Memory system issue. Some conversation history may be lost.",
            "fallback_mode": True
        }
    
    async def _handle_agent_error(
        self,
        error: AgnoEuriError,
        context: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None
    ) -> Dict[str, Any]:
        """Handle agent-related errors."""
        return {
            "status": "agent_error",
            "retry_possible": True,
            "message": "Agent processing error. Please rephrase your request and try again."
        }
    
    def _generate_user_friendly_message(self, error: AgnoEuriError) -> str:
        """Generate a user-friendly error message."""
        if error.category == ErrorCategory.API_ERROR:
            return "I'm having trouble connecting to the service. Please try again in a moment."
        elif error.category == ErrorCategory.NETWORK_ERROR:
            return "There seems to be a network connectivity issue. Please check your connection and try again."
        elif error.category == ErrorCategory.AUTHENTICATION_ERROR:
            return "There's an authentication issue. Please contact support if this persists."
        elif error.category == ErrorCategory.CACHE_ERROR:
            return "I'm experiencing some performance issues but can still help you."
        elif error.category == ErrorCategory.MEMORY_ERROR:
            return "I may not remember our previous conversation, but I'm ready to help with your current request."
        elif error.category == ErrorCategory.AGENT_ERROR:
            return "I encountered an issue processing your request. Could you please rephrase it?"
        else:
            return "I encountered an unexpected issue. Please try again or contact support if this persists."
    
    def _generate_suggestions(self, error: AgnoEuriError) -> List[str]:
        """Generate helpful suggestions for the user."""
        suggestions = []
        
        if error.category == ErrorCategory.API_ERROR:
            suggestions.extend([
                "Try again in a few moments",
                "Check if the service is experiencing issues",
                "Verify your internet connection"
            ])
        elif error.category == ErrorCategory.NETWORK_ERROR:
            suggestions.extend([
                "Check your internet connection",
                "Try refreshing the page",
                "Contact your network administrator if on a corporate network"
            ])
        elif error.category == ErrorCategory.AUTHENTICATION_ERROR:
            suggestions.extend([
                "Verify your API key is correct",
                "Check if your subscription is active",
                "Contact support for assistance"
            ])
        elif error.category == ErrorCategory.AGENT_ERROR:
            suggestions.extend([
                "Try rephrasing your question",
                "Break down complex requests into smaller parts",
                "Provide more specific details about what you need"
            ])
        else:
            suggestions.extend([
                "Try again in a moment",
                "Contact support if the issue persists"
            ])
        
        return suggestions
    
    async def handle_agent_error(
        self,
        error: Exception,
        user_message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Specific handler for agent errors with fallback response."""
        error_response = await self.handle_error(error, context, user_message)
        
        # Generate fallback response
        fallback_response = {
            "content": error_response["user_friendly_message"],
            "error": True,
            "suggestions": error_response["suggestions"],
            "retry_possible": error_response["retry_possible"]
        }
        
        return fallback_response
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics for monitoring."""
        total_errors = sum(self.error_counts.values())
        
        return {
            "total_errors": total_errors,
            "error_counts_by_category": self.error_counts.copy(),
            "error_rates": {
                category: (count / total_errors * 100) if total_errors > 0 else 0
                for category, count in self.error_counts.items()
            }
        }
