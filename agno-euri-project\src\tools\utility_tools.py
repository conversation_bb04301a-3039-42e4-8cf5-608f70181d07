"""
Additional utility tools for the Agno agent.
Provides general-purpose functionality and helper tools.
"""

import json
import re
import hashlib
import base64
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs
import asyncio

from agno.tools import Toolkit

from ..utils.logger import get_logger


class UtilityTools(Toolkit):
    """
    Collection of utility tools for data processing, validation, and transformation.
    """
    
    def __init__(self):
        """Initialize utility tools."""
        super().__init__(name="utility_tools")
        self.logger = get_logger(__name__)
        
        # Register all tools
        self.register(self.format_json)
        self.register(self.validate_json)
        self.register(self.extract_urls)
        self.register(self.extract_emails)
        self.register(self.hash_text)
        self.register(self.encode_base64)
        self.register(self.decode_base64)
        self.register(self.parse_url)
        self.register(self.format_datetime)
        self.register(self.calculate_time_difference)
        self.register(self.clean_text)
        self.register(self.count_words)
        self.register(self.extract_numbers)
        self.register(self.validate_email)
        self.register(self.generate_summary_stats)
    
    async def format_json(self, data: Union[str, Dict, List], indent: int = 2) -> str:
        """
        Format JSON data with proper indentation.
        
        Args:
            data: JSON data to format (string or object)
            indent: Number of spaces for indentation
        
        Returns:
            Formatted JSON string
        """
        try:
            if isinstance(data, str):
                # Try to parse if it's a JSON string
                parsed_data = json.loads(data)
            else:
                parsed_data = data
            
            formatted = json.dumps(parsed_data, indent=indent, ensure_ascii=False, default=str)
            self.logger.debug("JSON formatted successfully")
            return formatted
            
        except json.JSONDecodeError as e:
            error_msg = f"Invalid JSON data: {e}"
            self.logger.error(error_msg)
            return json.dumps({"error": error_msg})
        except Exception as e:
            error_msg = f"Error formatting JSON: {e}"
            self.logger.error(error_msg)
            return json.dumps({"error": error_msg})
    
    async def validate_json(self, json_string: str) -> str:
        """
        Validate JSON string and return validation results.
        
        Args:
            json_string: JSON string to validate
        
        Returns:
            JSON string with validation results
        """
        try:
            parsed = json.loads(json_string)
            result = {
                "valid": True,
                "message": "JSON is valid",
                "data_type": type(parsed).__name__,
                "size_bytes": len(json_string.encode('utf-8'))
            }
            
            if isinstance(parsed, dict):
                result["keys_count"] = len(parsed.keys())
            elif isinstance(parsed, list):
                result["items_count"] = len(parsed)
            
            return json.dumps(result, indent=2)
            
        except json.JSONDecodeError as e:
            result = {
                "valid": False,
                "error": str(e),
                "line": getattr(e, 'lineno', None),
                "column": getattr(e, 'colno', None)
            }
            return json.dumps(result, indent=2)
    
    async def extract_urls(self, text: str) -> str:
        """
        Extract URLs from text.
        
        Args:
            text: Text to extract URLs from
        
        Returns:
            JSON string with extracted URLs
        """
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        urls = re.findall(url_pattern, text)
        
        result = {
            "urls_found": len(urls),
            "urls": urls,
            "unique_domains": list(set([urlparse(url).netloc for url in urls]))
        }
        
        return json.dumps(result, indent=2)
    
    async def extract_emails(self, text: str) -> str:
        """
        Extract email addresses from text.
        
        Args:
            text: Text to extract emails from
        
        Returns:
            JSON string with extracted emails
        """
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        
        result = {
            "emails_found": len(emails),
            "emails": emails,
            "unique_domains": list(set([email.split('@')[1] for email in emails]))
        }
        
        return json.dumps(result, indent=2)
    
    async def hash_text(self, text: str, algorithm: str = "sha256") -> str:
        """
        Generate hash of text using specified algorithm.
        
        Args:
            text: Text to hash
            algorithm: Hash algorithm (md5, sha1, sha256, sha512)
        
        Returns:
            JSON string with hash results
        """
        try:
            text_bytes = text.encode('utf-8')
            
            if algorithm.lower() == "md5":
                hash_obj = hashlib.md5(text_bytes)
            elif algorithm.lower() == "sha1":
                hash_obj = hashlib.sha1(text_bytes)
            elif algorithm.lower() == "sha256":
                hash_obj = hashlib.sha256(text_bytes)
            elif algorithm.lower() == "sha512":
                hash_obj = hashlib.sha512(text_bytes)
            else:
                return json.dumps({"error": f"Unsupported algorithm: {algorithm}"})
            
            result = {
                "algorithm": algorithm.lower(),
                "hash": hash_obj.hexdigest(),
                "input_length": len(text),
                "hash_length": len(hash_obj.hexdigest())
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return json.dumps({"error": str(e)})
    
    async def encode_base64(self, text: str) -> str:
        """
        Encode text to base64.
        
        Args:
            text: Text to encode
        
        Returns:
            JSON string with encoded result
        """
        try:
            encoded_bytes = base64.b64encode(text.encode('utf-8'))
            encoded_text = encoded_bytes.decode('utf-8')
            
            result = {
                "original_text": text,
                "encoded": encoded_text,
                "original_length": len(text),
                "encoded_length": len(encoded_text)
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return json.dumps({"error": str(e)})
    
    async def decode_base64(self, encoded_text: str) -> str:
        """
        Decode base64 text.
        
        Args:
            encoded_text: Base64 encoded text
        
        Returns:
            JSON string with decoded result
        """
        try:
            decoded_bytes = base64.b64decode(encoded_text)
            decoded_text = decoded_bytes.decode('utf-8')
            
            result = {
                "encoded_text": encoded_text,
                "decoded": decoded_text,
                "encoded_length": len(encoded_text),
                "decoded_length": len(decoded_text)
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return json.dumps({"error": str(e)})
    
    async def parse_url(self, url: str) -> str:
        """
        Parse URL and extract components.
        
        Args:
            url: URL to parse
        
        Returns:
            JSON string with URL components
        """
        try:
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            
            result = {
                "original_url": url,
                "scheme": parsed.scheme,
                "netloc": parsed.netloc,
                "hostname": parsed.hostname,
                "port": parsed.port,
                "path": parsed.path,
                "query": parsed.query,
                "fragment": parsed.fragment,
                "query_parameters": query_params,
                "is_secure": parsed.scheme == "https"
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return json.dumps({"error": str(e)})
    
    async def format_datetime(
        self,
        datetime_str: str,
        input_format: str = "%Y-%m-%d %H:%M:%S",
        output_format: str = "%B %d, %Y at %I:%M %p"
    ) -> str:
        """
        Format datetime string.
        
        Args:
            datetime_str: Datetime string to format
            input_format: Input format pattern
            output_format: Output format pattern
        
        Returns:
            JSON string with formatted datetime
        """
        try:
            dt = datetime.strptime(datetime_str, input_format)
            formatted = dt.strftime(output_format)
            
            result = {
                "original": datetime_str,
                "formatted": formatted,
                "iso_format": dt.isoformat(),
                "timestamp": dt.timestamp(),
                "weekday": dt.strftime("%A"),
                "month": dt.strftime("%B"),
                "year": dt.year
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return json.dumps({"error": str(e)})
    
    async def calculate_time_difference(
        self,
        start_time: str,
        end_time: str,
        time_format: str = "%Y-%m-%d %H:%M:%S"
    ) -> str:
        """
        Calculate time difference between two datetime strings.
        
        Args:
            start_time: Start datetime string
            end_time: End datetime string
            time_format: Datetime format pattern
        
        Returns:
            JSON string with time difference
        """
        try:
            start_dt = datetime.strptime(start_time, time_format)
            end_dt = datetime.strptime(end_time, time_format)
            
            difference = end_dt - start_dt
            
            result = {
                "start_time": start_time,
                "end_time": end_time,
                "difference_seconds": difference.total_seconds(),
                "difference_minutes": difference.total_seconds() / 60,
                "difference_hours": difference.total_seconds() / 3600,
                "difference_days": difference.days,
                "human_readable": str(difference)
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return json.dumps({"error": str(e)})
    
    async def clean_text(self, text: str, options: Optional[Dict[str, bool]] = None) -> str:
        """
        Clean and normalize text.
        
        Args:
            text: Text to clean
            options: Cleaning options
        
        Returns:
            JSON string with cleaned text
        """
        if options is None:
            options = {
                "remove_extra_whitespace": True,
                "remove_special_chars": False,
                "lowercase": False,
                "remove_numbers": False
            }
        
        cleaned = text
        
        if options.get("remove_extra_whitespace", True):
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        if options.get("remove_special_chars", False):
            cleaned = re.sub(r'[^a-zA-Z0-9\s]', '', cleaned)
        
        if options.get("lowercase", False):
            cleaned = cleaned.lower()
        
        if options.get("remove_numbers", False):
            cleaned = re.sub(r'\d+', '', cleaned)
        
        result = {
            "original_text": text,
            "cleaned_text": cleaned,
            "original_length": len(text),
            "cleaned_length": len(cleaned),
            "options_used": options
        }
        
        return json.dumps(result, indent=2)
    
    async def count_words(self, text: str) -> str:
        """
        Count words, characters, and other text statistics.
        
        Args:
            text: Text to analyze
        
        Returns:
            JSON string with text statistics
        """
        words = text.split()
        sentences = re.split(r'[.!?]+', text)
        paragraphs = text.split('\n\n')
        
        result = {
            "character_count": len(text),
            "character_count_no_spaces": len(text.replace(' ', '')),
            "word_count": len(words),
            "sentence_count": len([s for s in sentences if s.strip()]),
            "paragraph_count": len([p for p in paragraphs if p.strip()]),
            "average_word_length": sum(len(word) for word in words) / len(words) if words else 0,
            "longest_word": max(words, key=len) if words else "",
            "shortest_word": min(words, key=len) if words else ""
        }
        
        return json.dumps(result, indent=2)
    
    async def extract_numbers(self, text: str) -> str:
        """
        Extract numbers from text.
        
        Args:
            text: Text to extract numbers from
        
        Returns:
            JSON string with extracted numbers
        """
        # Extract integers
        integers = re.findall(r'\b\d+\b', text)
        
        # Extract floats
        floats = re.findall(r'\b\d+\.\d+\b', text)
        
        # Extract percentages
        percentages = re.findall(r'\b\d+(?:\.\d+)?%\b', text)
        
        # Extract currency
        currency = re.findall(r'[$€£¥]\d+(?:\.\d{2})?|\d+(?:\.\d{2})?[$€£¥]', text)
        
        result = {
            "integers": [int(i) for i in integers],
            "floats": [float(f) for f in floats],
            "percentages": percentages,
            "currency": currency,
            "total_numbers_found": len(integers) + len(floats),
            "sum_integers": sum(int(i) for i in integers) if integers else 0,
            "sum_floats": sum(float(f) for f in floats) if floats else 0
        }
        
        return json.dumps(result, indent=2)
    
    async def validate_email(self, email: str) -> str:
        """
        Validate email address format.
        
        Args:
            email: Email address to validate
        
        Returns:
            JSON string with validation results
        """
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        is_valid = bool(re.match(email_pattern, email))
        
        result = {
            "email": email,
            "is_valid": is_valid,
            "domain": email.split('@')[1] if '@' in email else None,
            "local_part": email.split('@')[0] if '@' in email else None,
            "length": len(email)
        }
        
        if not is_valid:
            issues = []
            if '@' not in email:
                issues.append("Missing @ symbol")
            elif email.count('@') > 1:
                issues.append("Multiple @ symbols")
            elif not email.split('@')[0]:
                issues.append("Missing local part")
            elif not email.split('@')[1]:
                issues.append("Missing domain")
            elif '.' not in email.split('@')[1]:
                issues.append("Domain missing TLD")
            
            result["issues"] = issues
        
        return json.dumps(result, indent=2)
    
    async def generate_summary_stats(self, data: List[Union[int, float]]) -> str:
        """
        Generate summary statistics for numerical data.
        
        Args:
            data: List of numerical values
        
        Returns:
            JSON string with summary statistics
        """
        try:
            if not data:
                return json.dumps({"error": "No data provided"})
            
            sorted_data = sorted(data)
            n = len(data)
            
            # Basic statistics
            total = sum(data)
            mean = total / n
            
            # Median
            if n % 2 == 0:
                median = (sorted_data[n//2 - 1] + sorted_data[n//2]) / 2
            else:
                median = sorted_data[n//2]
            
            # Variance and standard deviation
            variance = sum((x - mean) ** 2 for x in data) / n
            std_dev = variance ** 0.5
            
            result = {
                "count": n,
                "sum": total,
                "mean": mean,
                "median": median,
                "mode": max(set(data), key=data.count) if data else None,
                "minimum": min(data),
                "maximum": max(data),
                "range": max(data) - min(data),
                "variance": variance,
                "standard_deviation": std_dev,
                "quartiles": {
                    "q1": sorted_data[n//4] if n >= 4 else None,
                    "q2": median,
                    "q3": sorted_data[3*n//4] if n >= 4 else None
                }
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return json.dumps({"error": str(e)})
