"""
FastAPI application for the Agno-Euri project.
Provides REST API endpoints for agent interaction and management.
"""

import asyncio
from typing import Dict, Any, List, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import uvicorn

from ..config.settings import get_settings
from ..config.loader import setup_configuration
from ..agents.euri_agent import EuriAgent
from ..utils.logger import get_logger, setup_logging
from ..utils.error_handler import <PERSON>rrorHandler
from .models import (
    ChatRequest, ChatResponse, AgentInfoResponse, HealthResponse,
    SessionRequest, SessionResponse, CacheStatsResponse
)
from .middleware import RateLimitMiddleware, LoggingMiddleware
from .dependencies import get_agent, get_error_handler


# Global variables
agent_instances: Dict[str, EuriAgent] = {}
settings = None
logger = None
error_handler = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global settings, logger, error_handler
    
    # Startup
    print("🚀 Starting Agno-Euri API server...")
    
    # Setup configuration and logging
    settings = setup_configuration()
    setup_logging()
    logger = get_logger(__name__)
    error_handler = ErrorHandler()
    
    logger.info("Agno-Euri API server starting up")
    logger.info(f"Environment: {settings.environment}")
    logger.info(f"Debug mode: {settings.debug}")
    
    # Initialize default agent
    try:
        default_agent = EuriAgent(agent_id="default")
        agent_instances["default"] = default_agent
        logger.info("Default agent initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize default agent: {e}")
    
    yield
    
    # Shutdown
    logger.info("Agno-Euri API server shutting down")
    
    # Cleanup agent instances
    for agent_id, agent in agent_instances.items():
        try:
            if hasattr(agent, 'cleanup'):
                await agent.cleanup()
        except Exception as e:
            logger.warning(f"Error cleaning up agent {agent_id}: {e}")
    
    agent_instances.clear()
    print("👋 Agno-Euri API server stopped")


# Create FastAPI app
app = FastAPI(
    title="Agno-Euri API",
    description="Advanced AI agent system with euri-client integration, caching, and conversational memory",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Security
security = HTTPBearer(auto_error=False)


# Middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(LoggingMiddleware)
app.add_middleware(RateLimitMiddleware)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Will be configured from settings
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def configure_cors():
    """Configure CORS with settings."""
    global settings
    if settings:
        # Update CORS origins from settings
        for middleware in app.user_middleware:
            if middleware.cls == CORSMiddleware:
                middleware.kwargs["allow_origins"] = settings.api.cors_origins
                break


# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    try:
        # Check default agent
        agent_health = {}
        if "default" in agent_instances:
            agent_health = await agent_instances["default"].health_check()
        
        return HealthResponse(
            status="healthy",
            timestamp=None,  # Will be set by the model
            version="1.0.0",
            environment=settings.environment if settings else "unknown",
            agent_health=agent_health
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


# Agent endpoints
@app.post("/chat", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    background_tasks: BackgroundTasks,
    agent: EuriAgent = Depends(get_agent),
    error_handler: ErrorHandler = Depends(get_error_handler)
):
    """
    Chat with the agent.
    
    Args:
        request: Chat request with message and options
        background_tasks: Background tasks for async operations
        agent: Agent instance
        error_handler: Error handler instance
    
    Returns:
        Agent response
    """
    try:
        logger.info(f"Processing chat request: {request.message[:100]}...")
        
        # Run agent
        response = await agent.run_async(
            message=request.message,
            stream=False,
            user_id=request.user_id,
            session_id=request.session_id
        )
        
        # Extract response content
        if hasattr(response, 'content'):
            content = response.content
        else:
            content = str(response)
        
        return ChatResponse(
            response=content,
            session_id=agent.agent.session_id,
            user_id=agent.agent.user_id,
            agent_id=agent.agent.agent_id,
            metadata={
                "model": settings.ai_model.agno_model if settings else "unknown",
                "cached": False,  # TODO: Implement cache detection
                "processing_time": None  # TODO: Implement timing
            }
        )
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        error_response = await error_handler.handle_agent_error(e, request.message)
        
        return ChatResponse(
            response=error_response["content"],
            session_id=request.session_id,
            user_id=request.user_id,
            error=True,
            metadata={
                "error_type": type(e).__name__,
                "suggestions": error_response.get("suggestions", [])
            }
        )


@app.post("/chat/stream")
async def chat_stream(
    request: ChatRequest,
    agent: EuriAgent = Depends(get_agent),
    error_handler: ErrorHandler = Depends(get_error_handler)
):
    """
    Stream chat response from the agent.
    
    Args:
        request: Chat request with message and options
        agent: Agent instance
        error_handler: Error handler instance
    
    Returns:
        Streaming response
    """
    async def generate_response():
        try:
            response_stream = await agent.run_async(
                message=request.message,
                stream=True,
                user_id=request.user_id,
                session_id=request.session_id
            )
            
            async for chunk in response_stream:
                if hasattr(chunk, 'content'):
                    yield f"data: {chunk.content}\n\n"
                else:
                    yield f"data: {str(chunk)}\n\n"
            
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            logger.error(f"Streaming error: {e}")
            error_response = await error_handler.handle_agent_error(e, request.message)
            yield f"data: {error_response['content']}\n\n"
            yield "data: [ERROR]\n\n"
    
    return StreamingResponse(
        generate_response(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )


@app.get("/agent/info", response_model=AgentInfoResponse)
async def get_agent_info(agent: EuriAgent = Depends(get_agent)):
    """Get agent information."""
    try:
        info = agent.get_agent_info()
        return AgentInfoResponse(**info)
    except Exception as e:
        logger.error(f"Error getting agent info: {e}")
        raise HTTPException(status_code=500, detail="Failed to get agent info")


@app.post("/session/create", response_model=SessionResponse)
async def create_session(request: SessionRequest):
    """Create a new session."""
    try:
        # Create new agent instance for the session
        agent = EuriAgent(
            user_id=request.user_id,
            session_id=request.session_id,
            custom_instructions=request.custom_instructions
        )
        
        session_id = agent.agent.session_id
        agent_instances[session_id] = agent
        
        logger.info(f"Created new session: {session_id}")
        
        return SessionResponse(
            session_id=session_id,
            user_id=agent.agent.user_id,
            agent_id=agent.agent.agent_id,
            created=True
        )
        
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        raise HTTPException(status_code=500, detail="Failed to create session")


@app.get("/session/{session_id}/history")
async def get_session_history(session_id: str):
    """Get conversation history for a session."""
    try:
        if session_id not in agent_instances:
            raise HTTPException(status_code=404, detail="Session not found")
        
        agent = agent_instances[session_id]
        history = await agent.get_conversation_history()
        
        return {"session_id": session_id, "history": history}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session history: {e}")
        raise HTTPException(status_code=500, detail="Failed to get session history")


@app.delete("/session/{session_id}")
async def delete_session(session_id: str):
    """Delete a session and clear its history."""
    try:
        if session_id not in agent_instances:
            raise HTTPException(status_code=404, detail="Session not found")
        
        agent = agent_instances[session_id]
        await agent.clear_conversation_history()
        
        # Remove from instances
        del agent_instances[session_id]
        
        logger.info(f"Deleted session: {session_id}")
        
        return {"message": "Session deleted successfully", "session_id": session_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete session")


@app.get("/cache/stats", response_model=CacheStatsResponse)
async def get_cache_stats(agent: EuriAgent = Depends(get_agent)):
    """Get cache statistics."""
    try:
        if not agent.cache:
            raise HTTPException(status_code=404, detail="Cache not enabled")
        
        stats = agent.cache.get_stats()
        health = await agent.cache.health_check()
        
        return CacheStatsResponse(
            **stats,
            health=health
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get cache stats")


@app.post("/cache/clear")
async def clear_cache(namespace: Optional[str] = None, agent: EuriAgent = Depends(get_agent)):
    """Clear cache."""
    try:
        if not agent.cache:
            raise HTTPException(status_code=404, detail="Cache not enabled")
        
        success = await agent.cache.clear(namespace)
        
        if success:
            return {"message": f"Cache cleared successfully", "namespace": namespace}
        else:
            raise HTTPException(status_code=500, detail="Failed to clear cache")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(status_code=500, detail="Failed to clear cache")


# Admin endpoints
@app.get("/admin/stats")
async def get_admin_stats(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get administrative statistics."""
    # TODO: Implement proper authentication
    try:
        stats = {
            "active_sessions": len(agent_instances),
            "session_ids": list(agent_instances.keys()),
            "environment": settings.environment if settings else "unknown",
            "features": {
                "caching": settings.features.enable_caching if settings else False,
                "memory": settings.features.enable_memory_persistence if settings else False,
                "conversation_history": settings.features.enable_conversation_history if settings else False
            }
        }
        
        # Add error statistics if available
        if error_handler:
            stats["errors"] = error_handler.get_error_statistics()
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting admin stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get admin stats")


# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code,
            "path": str(request.url)
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": True,
            "message": "Internal server error",
            "type": type(exc).__name__,
            "path": str(request.url)
        }
    )


def run_server():
    """Run the FastAPI server."""
    settings = get_settings()
    
    uvicorn.run(
        "src.api.main:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.reload,
        workers=settings.api.workers,
        log_level=settings.api.log_level,
        access_log=True
    )


if __name__ == "__main__":
    run_server()
