"""
Configuration loader and validation utilities.
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional

from dotenv import load_dotenv

from .settings import Settings, get_settings


def load_environment(env_file: Optional[str] = None) -> None:
    """
    Load environment variables from .env file.
    
    Args:
        env_file: Path to the .env file. If None, looks for .env in current directory.
    """
    if env_file is None:
        env_file = ".env"
    
    env_path = Path(env_file)
    if env_path.exists():
        load_dotenv(env_path)
        print(f"Loaded environment from {env_path}")
    else:
        print(f"Environment file {env_path} not found, using system environment variables")


def validate_configuration() -> Dict[str, Any]:
    """
    Validate the current configuration and return validation results.
    
    Returns:
        Dictionary containing validation results and any errors.
    """
    settings = get_settings()
    validation_results = {
        "valid": True,
        "errors": [],
        "warnings": [],
        "info": []
    }
    
    # Validate required API keys
    if not settings.euri.api_key or settings.euri.api_key == "your_euri_api_key_here":
        validation_results["errors"].append("EURI_API_KEY is not set or using default value")
        validation_results["valid"] = False
    
    if not settings.ai_model.openai_api_key and not settings.ai_model.anthropic_api_key:
        validation_results["errors"].append("No AI model API key configured (OpenAI or Anthropic)")
        validation_results["valid"] = False
    
    if settings.api.secret_key == "your_super_secret_key_here_change_this_in_production":
        if settings.is_production:
            validation_results["errors"].append("SECRET_KEY is using default value in production")
            validation_results["valid"] = False
        else:
            validation_results["warnings"].append("SECRET_KEY is using default value")
    
    # Validate Redis connection if caching is enabled
    if settings.features.enable_caching:
        try:
            import redis
            r = redis.from_url(settings.cache.redis_url)
            r.ping()
            validation_results["info"].append("Redis connection successful")
        except Exception as e:
            validation_results["warnings"].append(f"Redis connection failed: {e}")
            validation_results["warnings"].append("Caching will be disabled")
    
    # Validate database configuration
    if "postgresql" in settings.database.database_url:
        validation_results["info"].append("Using PostgreSQL database")
    elif "sqlite" in settings.database.database_url:
        validation_results["info"].append("Using SQLite database")
        # Ensure SQLite directory exists
        db_path = Path(settings.database.database_url.replace("sqlite+aiosqlite:///", ""))
        db_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Validate log directory
    log_path = Path(settings.logging.log_file)
    if not log_path.parent.exists():
        try:
            log_path.parent.mkdir(parents=True, exist_ok=True)
            validation_results["info"].append(f"Created log directory: {log_path.parent}")
        except Exception as e:
            validation_results["warnings"].append(f"Could not create log directory: {e}")
    
    return validation_results


def print_configuration_summary() -> None:
    """Print a summary of the current configuration."""
    settings = get_settings()
    
    print("\n" + "="*60)
    print("AGNO-EURI PROJECT CONFIGURATION SUMMARY")
    print("="*60)
    
    print(f"Environment: {settings.environment}")
    print(f"Debug Mode: {settings.debug}")
    print(f"Testing Mode: {settings.testing}")
    
    print("\n--- API Configuration ---")
    print(f"Host: {settings.api.host}")
    print(f"Port: {settings.api.port}")
    print(f"Reload: {settings.api.reload}")
    
    print("\n--- AI Model Configuration ---")
    print(f"Primary Model: {settings.ai_model.agno_model}")
    print(f"OpenAI Model: {settings.ai_model.openai_model}")
    print(f"Anthropic Model: {settings.ai_model.anthropic_model}")
    
    print("\n--- Euri Configuration ---")
    print(f"Client URL: {settings.euri.client_url}")
    print(f"Timeout: {settings.euri.timeout}s")
    print(f"Max Retries: {settings.euri.max_retries}")
    
    print("\n--- Cache Configuration ---")
    print(f"Redis URL: {settings.cache.redis_url}")
    print(f"Default TTL: {settings.cache.cache_ttl_default}s")
    print(f"Max Size: {settings.cache.cache_max_size}")
    
    print("\n--- Memory Configuration ---")
    print(f"Backend: {settings.memory.memory_backend}")
    print(f"Session Timeout: {settings.memory.session_timeout}s")
    print(f"Max History: {settings.memory.max_conversation_history}")
    
    print("\n--- Feature Flags ---")
    print(f"Caching: {settings.features.enable_caching}")
    print(f"Memory Persistence: {settings.features.enable_memory_persistence}")
    print(f"Conversation History: {settings.features.enable_conversation_history}")
    print(f"Metrics Collection: {settings.features.enable_metrics_collection}")
    
    print("\n--- Database ---")
    print(f"Database URL: {settings.database.database_url}")
    
    print("\n--- Logging ---")
    print(f"Log Level: {settings.logging.log_level}")
    print(f"Log File: {settings.logging.log_file}")
    print(f"Log Format: {settings.logging.log_format}")
    
    print("="*60)


def setup_configuration(env_file: Optional[str] = None, validate: bool = True) -> Settings:
    """
    Complete configuration setup including loading, validation, and summary.
    
    Args:
        env_file: Path to the .env file
        validate: Whether to validate the configuration
    
    Returns:
        Configured Settings instance
    """
    # Load environment variables
    load_environment(env_file)
    
    # Get settings
    settings = get_settings()
    
    # Validate configuration if requested
    if validate:
        validation_results = validate_configuration()
        
        if validation_results["errors"]:
            print("\n❌ Configuration Errors:")
            for error in validation_results["errors"]:
                print(f"  - {error}")
        
        if validation_results["warnings"]:
            print("\n⚠️  Configuration Warnings:")
            for warning in validation_results["warnings"]:
                print(f"  - {warning}")
        
        if validation_results["info"]:
            print("\n✅ Configuration Info:")
            for info in validation_results["info"]:
                print(f"  - {info}")
        
        if not validation_results["valid"]:
            print("\n❌ Configuration validation failed!")
            sys.exit(1)
        else:
            print("\n✅ Configuration validation passed!")
    
    return settings


if __name__ == "__main__":
    """Run configuration validation and summary."""
    settings = setup_configuration()
    print_configuration_summary()
