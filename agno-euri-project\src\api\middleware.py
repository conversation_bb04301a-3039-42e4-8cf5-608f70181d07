"""
FastAPI middleware for the Agno-Euri project.
"""

import time
import json
from typing import Dict, Any, Optional
from collections import defaultdict, deque
from datetime import datetime, timedelta

from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from ..config.settings import get_settings
from ..utils.logger import get_logger


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request/response logging."""
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = get_logger(__name__)
    
    async def dispatch(self, request: Request, call_next):
        """Process request and log details."""
        start_time = time.time()
        
        # Log request
        self.logger.info(
            f"Request: {request.method} {request.url.path}",
            extra={
                "method": request.method,
                "path": request.url.path,
                "query_params": dict(request.query_params),
                "client_ip": request.client.host if request.client else None,
                "user_agent": request.headers.get("user-agent"),
            }
        )
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log response
            self.logger.info(
                f"Response: {response.status_code} - {process_time:.3f}s",
                extra={
                    "status_code": response.status_code,
                    "process_time": process_time,
                    "path": request.url.path,
                }
            )
            
            # Add timing header
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            self.logger.error(
                f"Request failed: {e} - {process_time:.3f}s",
                extra={
                    "error": str(e),
                    "process_time": process_time,
                    "path": request.url.path,
                }
            )
            raise


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware for rate limiting requests."""
    
    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        
        # Rate limiting storage
        self.request_counts: Dict[str, deque] = defaultdict(deque)
        self.blocked_ips: Dict[str, datetime] = {}
        
        # Configuration
        self.max_requests = self.settings.security.rate_limit_requests
        self.window_seconds = self.settings.security.rate_limit_window
        self.block_duration = timedelta(minutes=5)  # Block for 5 minutes
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        return request.client.host if request.client else "unknown"
    
    def _is_rate_limited(self, client_ip: str) -> bool:
        """Check if client is rate limited."""
        now = datetime.utcnow()
        
        # Check if IP is currently blocked
        if client_ip in self.blocked_ips:
            if now < self.blocked_ips[client_ip]:
                return True
            else:
                # Block expired, remove it
                del self.blocked_ips[client_ip]
        
        # Get request history for this IP
        requests = self.request_counts[client_ip]
        
        # Remove old requests outside the window
        cutoff_time = now - timedelta(seconds=self.window_seconds)
        while requests and requests[0] < cutoff_time:
            requests.popleft()
        
        # Check if limit exceeded
        if len(requests) >= self.max_requests:
            # Block the IP
            self.blocked_ips[client_ip] = now + self.block_duration
            self.logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            return True
        
        # Add current request
        requests.append(now)
        return False
    
    def _cleanup_old_data(self):
        """Clean up old rate limiting data."""
        now = datetime.utcnow()
        cutoff_time = now - timedelta(hours=1)  # Keep data for 1 hour
        
        # Clean up request counts
        for ip, requests in list(self.request_counts.items()):
            while requests and requests[0] < cutoff_time:
                requests.popleft()
            
            # Remove empty entries
            if not requests:
                del self.request_counts[ip]
        
        # Clean up expired blocks
        expired_blocks = [
            ip for ip, block_time in self.blocked_ips.items()
            if now >= block_time
        ]
        for ip in expired_blocks:
            del self.blocked_ips[ip]
    
    async def dispatch(self, request: Request, call_next):
        """Process request with rate limiting."""
        # Skip rate limiting for health checks
        if request.url.path in ["/health", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)
        
        client_ip = self._get_client_ip(request)
        
        # Check rate limit
        if self._is_rate_limited(client_ip):
            self.logger.warning(f"Rate limit exceeded for {client_ip}")
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Limit: {self.max_requests} per {self.window_seconds} seconds",
                    "retry_after": self.window_seconds
                },
                headers={"Retry-After": str(self.window_seconds)}
            )
        
        # Periodically cleanup old data
        if len(self.request_counts) % 100 == 0:
            self._cleanup_old_data()
        
        return await call_next(request)


class SecurityMiddleware(BaseHTTPMiddleware):
    """Middleware for security headers and validation."""
    
    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
        self.logger = get_logger(__name__)
    
    async def dispatch(self, request: Request, call_next):
        """Process request with security enhancements."""
        # Check request size
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.settings.security.max_request_size:
            return JSONResponse(
                status_code=413,
                content={
                    "error": "Request too large",
                    "message": f"Request size exceeds limit of {self.settings.security.max_request_size} bytes"
                }
            )
        
        # Process request
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        
        # Add custom headers
        response.headers["X-API-Version"] = "1.0.0"
        response.headers["X-Powered-By"] = "Agno-Euri"
        
        return response


class CacheMiddleware(BaseHTTPMiddleware):
    """Middleware for HTTP caching."""
    
    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        
        # Cache configuration for different endpoints
        self.cache_config = {
            "/health": {"max_age": 30, "public": True},
            "/agent/info": {"max_age": 300, "public": False},
            "/cache/stats": {"max_age": 60, "public": False},
        }
    
    async def dispatch(self, request: Request, call_next):
        """Process request with caching headers."""
        response = await call_next(request)
        
        # Add cache headers based on endpoint
        path = request.url.path
        cache_settings = self.cache_config.get(path)
        
        if cache_settings and request.method == "GET":
            max_age = cache_settings["max_age"]
            is_public = cache_settings["public"]
            
            if is_public:
                response.headers["Cache-Control"] = f"public, max-age={max_age}"
            else:
                response.headers["Cache-Control"] = f"private, max-age={max_age}"
            
            # Add ETag for better caching
            if hasattr(response, "body"):
                import hashlib
                etag = hashlib.md5(response.body).hexdigest()[:16]
                response.headers["ETag"] = f'"{etag}"'
        else:
            # No cache for other endpoints
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
        
        return response


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting metrics."""
    
    def __init__(self, app):
        super().__init__(app)
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        
        # Metrics storage
        self.request_count = 0
        self.error_count = 0
        self.response_times = deque(maxlen=1000)  # Keep last 1000 response times
        self.endpoint_stats = defaultdict(lambda: {"count": 0, "errors": 0, "total_time": 0})
    
    async def dispatch(self, request: Request, call_next):
        """Process request and collect metrics."""
        start_time = time.time()
        self.request_count += 1
        
        try:
            response = await call_next(request)
            
            # Calculate response time
            response_time = time.time() - start_time
            self.response_times.append(response_time)
            
            # Update endpoint stats
            endpoint = f"{request.method} {request.url.path}"
            stats = self.endpoint_stats[endpoint]
            stats["count"] += 1
            stats["total_time"] += response_time
            
            if response.status_code >= 400:
                self.error_count += 1
                stats["errors"] += 1
            
            # Add metrics headers
            response.headers["X-Request-Count"] = str(self.request_count)
            response.headers["X-Response-Time"] = f"{response_time:.3f}"
            
            return response
            
        except Exception as e:
            self.error_count += 1
            response_time = time.time() - start_time
            
            endpoint = f"{request.method} {request.url.path}"
            stats = self.endpoint_stats[endpoint]
            stats["count"] += 1
            stats["errors"] += 1
            stats["total_time"] += response_time
            
            raise
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get collected metrics."""
        avg_response_time = (
            sum(self.response_times) / len(self.response_times)
            if self.response_times else 0
        )
        
        return {
            "total_requests": self.request_count,
            "total_errors": self.error_count,
            "error_rate": (self.error_count / self.request_count * 100) if self.request_count > 0 else 0,
            "average_response_time": avg_response_time,
            "endpoint_stats": dict(self.endpoint_stats),
            "recent_response_times": list(self.response_times)[-10:]  # Last 10 response times
        }
