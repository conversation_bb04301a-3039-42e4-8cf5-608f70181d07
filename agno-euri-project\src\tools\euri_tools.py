"""
Euri-specific tools for the Agno agent.
Provides integration with euri-client and euri-api_key functionality.
"""

import asyncio
import json
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta

import httpx
from agno.tools import Toolkit

from ..config.settings import get_settings
from ..utils.logger import get_logger
from ..cache.agent_cache import AgentCache


class EuriTools(Toolkit):
    """
    Comprehensive toolkit for euri-client integration.
    Provides methods for data retrieval, processing, and analysis using euri services.
    """
    
    def __init__(
        self,
        api_key: str,
        base_url: str = "https://api.euri.com/v1",
        timeout: int = 30,
        max_retries: int = 3,
        cache: Optional[AgentCache] = None
    ):
        """
        Initialize EuriTools.
        
        Args:
            api_key: Euri API key
            base_url: Base URL for euri API
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
            cache: Cache instance for response caching
        """
        super().__init__(name="euri_tools")
        
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        self.cache = cache
        self.logger = get_logger(__name__)
        
        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            timeout=timeout,
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
                "User-Agent": "agno-euri-client/1.0.0"
            }
        )
        
        # Register tools
        self.register(self.query_euri_data)
        self.register(self.process_euri_document)
        self.register(self.analyze_euri_content)
        self.register(self.search_euri_knowledge)
        self.register(self.get_euri_insights)
        self.register(self.validate_euri_data)
        self.register(self.transform_euri_format)
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Make HTTP request to euri API with retry logic and caching.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request body data
            params: Query parameters
            use_cache: Whether to use caching
        
        Returns:
            API response data
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        cache_key = f"euri_request:{method}:{url}:{json.dumps(data or {})}:{json.dumps(params or {})}"
        
        # Check cache first
        if use_cache and self.cache:
            cached_response = await self.cache.get(cache_key)
            if cached_response:
                self.logger.info(f"Returning cached response for {endpoint}")
                return cached_response
        
        # Make request with retry logic
        last_exception = None
        for attempt in range(self.max_retries + 1):
            try:
                self.logger.info(f"Making {method} request to {endpoint} (attempt {attempt + 1})")
                
                if method.upper() == "GET":
                    response = await self.client.get(url, params=params)
                elif method.upper() == "POST":
                    response = await self.client.post(url, json=data, params=params)
                elif method.upper() == "PUT":
                    response = await self.client.put(url, json=data, params=params)
                elif method.upper() == "DELETE":
                    response = await self.client.delete(url, params=params)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                response.raise_for_status()
                result = response.json()
                
                # Cache successful response
                if use_cache and self.cache:
                    await self.cache.set(cache_key, result, ttl=1800)  # 30 minutes
                
                return result
                
            except httpx.HTTPStatusError as e:
                last_exception = e
                if e.response.status_code == 429:  # Rate limited
                    wait_time = 2 ** attempt
                    self.logger.warning(f"Rate limited, waiting {wait_time}s before retry")
                    await asyncio.sleep(wait_time)
                elif e.response.status_code >= 500:  # Server error
                    wait_time = 2 ** attempt
                    self.logger.warning(f"Server error {e.response.status_code}, waiting {wait_time}s before retry")
                    await asyncio.sleep(wait_time)
                else:
                    # Client error, don't retry
                    break
            except Exception as e:
                last_exception = e
                wait_time = 2 ** attempt
                self.logger.warning(f"Request failed: {e}, waiting {wait_time}s before retry")
                await asyncio.sleep(wait_time)
        
        # All retries failed
        raise Exception(f"Failed to make request after {self.max_retries + 1} attempts: {last_exception}")
    
    async def query_euri_data(
        self,
        query: str,
        filters: Optional[Dict] = None,
        limit: int = 10,
        offset: int = 0
    ) -> str:
        """
        Query euri data with advanced filtering and pagination.
        
        Args:
            query: Search query string
            filters: Optional filters to apply
            limit: Maximum number of results
            offset: Pagination offset
        
        Returns:
            JSON string containing query results
        """
        try:
            params = {
                "q": query,
                "limit": limit,
                "offset": offset
            }
            
            if filters:
                params.update(filters)
            
            result = await self._make_request("GET", "/data/query", params=params)
            
            self.logger.info(f"Query '{query}' returned {len(result.get('results', []))} results")
            return json.dumps(result, indent=2)
            
        except Exception as e:
            self.logger.error(f"Error querying euri data: {e}")
            return json.dumps({"error": str(e), "query": query})
    
    async def process_euri_document(
        self,
        document_id: str,
        processing_type: str = "extract",
        options: Optional[Dict] = None
    ) -> str:
        """
        Process a document using euri services.
        
        Args:
            document_id: ID of the document to process
            processing_type: Type of processing (extract, analyze, summarize)
            options: Additional processing options
        
        Returns:
            JSON string containing processing results
        """
        try:
            data = {
                "document_id": document_id,
                "type": processing_type,
                "options": options or {}
            }
            
            result = await self._make_request("POST", "/documents/process", data=data)
            
            self.logger.info(f"Processed document {document_id} with type {processing_type}")
            return json.dumps(result, indent=2)
            
        except Exception as e:
            self.logger.error(f"Error processing document {document_id}: {e}")
            return json.dumps({"error": str(e), "document_id": document_id})
    
    async def analyze_euri_content(
        self,
        content: str,
        analysis_type: str = "sentiment",
        language: str = "auto"
    ) -> str:
        """
        Analyze content using euri analysis services.
        
        Args:
            content: Content to analyze
            analysis_type: Type of analysis (sentiment, entities, topics, etc.)
            language: Language code or 'auto' for auto-detection
        
        Returns:
            JSON string containing analysis results
        """
        try:
            data = {
                "content": content,
                "analysis_type": analysis_type,
                "language": language
            }
            
            result = await self._make_request("POST", "/analysis/content", data=data)
            
            self.logger.info(f"Analyzed content with type {analysis_type}")
            return json.dumps(result, indent=2)
            
        except Exception as e:
            self.logger.error(f"Error analyzing content: {e}")
            return json.dumps({"error": str(e), "analysis_type": analysis_type})
    
    async def search_euri_knowledge(
        self,
        query: str,
        knowledge_base: str = "default",
        semantic_search: bool = True,
        max_results: int = 5
    ) -> str:
        """
        Search euri knowledge base with semantic capabilities.
        
        Args:
            query: Search query
            knowledge_base: Knowledge base identifier
            semantic_search: Whether to use semantic search
            max_results: Maximum number of results
        
        Returns:
            JSON string containing search results
        """
        try:
            params = {
                "q": query,
                "kb": knowledge_base,
                "semantic": semantic_search,
                "max_results": max_results
            }
            
            result = await self._make_request("GET", "/knowledge/search", params=params)
            
            self.logger.info(f"Knowledge search for '{query}' returned {len(result.get('results', []))} results")
            return json.dumps(result, indent=2)
            
        except Exception as e:
            self.logger.error(f"Error searching knowledge base: {e}")
            return json.dumps({"error": str(e), "query": query})
    
    async def get_euri_insights(
        self,
        data_source: str,
        insight_type: str = "trends",
        time_range: str = "7d"
    ) -> str:
        """
        Get insights from euri data sources.
        
        Args:
            data_source: Data source identifier
            insight_type: Type of insights (trends, patterns, anomalies)
            time_range: Time range for analysis (1d, 7d, 30d, etc.)
        
        Returns:
            JSON string containing insights
        """
        try:
            params = {
                "source": data_source,
                "type": insight_type,
                "range": time_range
            }
            
            result = await self._make_request("GET", "/insights/generate", params=params)
            
            self.logger.info(f"Generated {insight_type} insights for {data_source}")
            return json.dumps(result, indent=2)
            
        except Exception as e:
            self.logger.error(f"Error generating insights: {e}")
            return json.dumps({"error": str(e), "data_source": data_source})
    
    async def validate_euri_data(
        self,
        data: Dict[str, Any],
        schema_name: str,
        strict_mode: bool = True
    ) -> str:
        """
        Validate data against euri schemas.
        
        Args:
            data: Data to validate
            schema_name: Name of the validation schema
            strict_mode: Whether to use strict validation
        
        Returns:
            JSON string containing validation results
        """
        try:
            request_data = {
                "data": data,
                "schema": schema_name,
                "strict": strict_mode
            }
            
            result = await self._make_request("POST", "/validation/validate", data=request_data)
            
            self.logger.info(f"Validated data against schema {schema_name}")
            return json.dumps(result, indent=2)
            
        except Exception as e:
            self.logger.error(f"Error validating data: {e}")
            return json.dumps({"error": str(e), "schema": schema_name})
    
    async def transform_euri_format(
        self,
        data: Dict[str, Any],
        source_format: str,
        target_format: str,
        options: Optional[Dict] = None
    ) -> str:
        """
        Transform data between different formats using euri services.
        
        Args:
            data: Data to transform
            source_format: Source data format
            target_format: Target data format
            options: Transformation options
        
        Returns:
            JSON string containing transformed data
        """
        try:
            request_data = {
                "data": data,
                "source_format": source_format,
                "target_format": target_format,
                "options": options or {}
            }
            
            result = await self._make_request("POST", "/transform/format", data=request_data)
            
            self.logger.info(f"Transformed data from {source_format} to {target_format}")
            return json.dumps(result, indent=2)
            
        except Exception as e:
            self.logger.error(f"Error transforming data: {e}")
            return json.dumps({"error": str(e), "source_format": source_format, "target_format": target_format})
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.aclose()
    
    def __del__(self):
        """Cleanup when object is destroyed."""
        if hasattr(self, 'client'):
            asyncio.create_task(self.client.aclose())
